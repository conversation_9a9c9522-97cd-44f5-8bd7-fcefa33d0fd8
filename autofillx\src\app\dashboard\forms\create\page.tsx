"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import DashboardLayout from "@/components/dashboard/layout"
import { 
  Sparkles, 
  Plus, 
  Trash2, 
  GripVertical,
  Eye,
  Save
} from "lucide-react"

interface FormField {
  id: string
  type: string
  label: string
  placeholder?: string
  required: boolean
  options?: string[]
}

interface BusinessSegment {
  name: string
  description: string
}

export default function CreateFormPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [formTitle, setFormTitle] = useState("")
  const [formDescription, setFormDescription] = useState("")
  const [businessType, setBusinessType] = useState("")
  const [customDescription, setCustomDescription] = useState("")
  const [fields, setFields] = useState<FormField[]>([])
  const [businessSegments, setBusinessSegments] = useState<BusinessSegment[]>([])
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  useEffect(() => {
    if (status === "loading") return
    if (!session) router.push("/login")
    
    // Load business segments
    fetchBusinessSegments()
  }, [session, status, router])

  const fetchBusinessSegments = async () => {
    try {
      const response = await fetch("/api/forms/suggestions")
      const data = await response.json()
      setBusinessSegments(data.segments || [])
    } catch (error) {
      console.error("Error fetching business segments:", error)
    }
  }

  const generateSuggestions = async () => {
    if (!businessType) return
    
    setIsLoadingSuggestions(true)
    try {
      const response = await fetch("/api/forms/suggestions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          businessType,
          customDescription,
        }),
      })
      
      const data = await response.json()
      if (data.suggestions) {
        const newFields = data.suggestions.map((suggestion: any, index: number) => ({
          id: `field-${Date.now()}-${index}`,
          ...suggestion,
        }))
        setFields(newFields)
      }
    } catch (error) {
      console.error("Error generating suggestions:", error)
    } finally {
      setIsLoadingSuggestions(false)
    }
  }

  const addField = () => {
    const newField: FormField = {
      id: `field-${Date.now()}`,
      type: "TEXT",
      label: "Novo campo",
      placeholder: "",
      required: false,
    }
    setFields([...fields, newField])
  }

  const updateField = (id: string, updates: Partial<FormField>) => {
    setFields(fields.map(field => 
      field.id === id ? { ...field, ...updates } : field
    ))
  }

  const removeField = (id: string) => {
    setFields(fields.filter(field => field.id !== id))
  }

  const fieldTypes = [
    { value: "TEXT", label: "Texto" },
    { value: "EMAIL", label: "Email" },
    { value: "PHONE", label: "Telefone" },
    { value: "NUMBER", label: "Número" },
    { value: "TEXTAREA", label: "Texto longo" },
    { value: "SELECT", label: "Seleção" },
    { value: "RADIO", label: "Múltipla escolha" },
    { value: "CHECKBOX", label: "Checkbox" },
    { value: "DATE", label: "Data" },
    { value: "FILE", label: "Arquivo" },
    { value: "CPF", label: "CPF" },
    { value: "CNPJ", label: "CNPJ" },
  ]

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) return null

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Criar Formulário</h1>
            <p className="text-gray-600">Use IA para gerar campos automaticamente</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Eye className="h-4 w-4" />
              <span>Preview</span>
            </button>
            <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
              <Save className="h-4 w-4" />
              <span>Salvar</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Form Builder */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Info */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h2 className="text-lg font-semibold mb-4">Informações Básicas</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Título do Formulário
                  </label>
                  <input
                    type="text"
                    value={formTitle}
                    onChange={(e) => setFormTitle(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ex: Formulário de Financiamento"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Descrição
                  </label>
                  <textarea
                    value={formDescription}
                    onChange={(e) => setFormDescription(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Descreva o propósito do formulário..."
                  />
                </div>
              </div>
            </div>

            {/* AI Assistant */}
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border border-purple-200">
              <div className="flex items-center space-x-2 mb-4">
                <Sparkles className="h-5 w-5 text-purple-600" />
                <h2 className="text-lg font-semibold text-gray-900">IA Assistente</h2>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Negócio
                  </label>
                  <select
                    value={businessType}
                    onChange={(e) => setBusinessType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Selecione um tipo</option>
                    {businessSegments.map((segment) => (
                      <option key={segment.name} value={segment.name}>
                        {segment.description}
                      </option>
                    ))}
                    <option value="custom">Outro (personalizado)</option>
                  </select>
                </div>
                
                {businessType === "custom" && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Descreva seu negócio
                    </label>
                    <textarea
                      value={customDescription}
                      onChange={(e) => setCustomDescription(e.target.value)}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Ex: Loja de móveis que oferece crediário..."
                    />
                  </div>
                )}
                
                <button
                  onClick={generateSuggestions}
                  disabled={!businessType || isLoadingSuggestions}
                  className="w-full flex items-center justify-center space-x-2 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoadingSuggestions ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Sparkles className="h-4 w-4" />
                  )}
                  <span>
                    {isLoadingSuggestions ? "Gerando..." : "Gerar Campos com IA"}
                  </span>
                </button>
              </div>
            </div>

            {/* Form Fields */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Campos do Formulário</h2>
                <button
                  onClick={addField}
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
                >
                  <Plus className="h-4 w-4" />
                  <span>Adicionar Campo</span>
                </button>
              </div>
              
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <GripVertical className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium text-gray-700">
                          Campo {index + 1}
                        </span>
                      </div>
                      <button
                        onClick={() => removeField(field.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Tipo
                        </label>
                        <select
                          value={field.type}
                          onChange={(e) => updateField(field.id, { type: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          {fieldTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Label
                        </label>
                        <input
                          type="text"
                          value={field.label}
                          onChange={(e) => updateField(field.id, { label: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Placeholder
                        </label>
                        <input
                          type="text"
                          value={field.placeholder || ""}
                          onChange={(e) => updateField(field.id, { placeholder: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={field.required}
                          onChange={(e) => updateField(field.id, { required: e.target.checked })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 text-sm text-gray-700">
                          Campo obrigatório
                        </label>
                      </div>
                    </div>
                  </div>
                ))}
                
                {fields.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Nenhum campo adicionado ainda</p>
                    <p className="text-sm">Use a IA ou adicione campos manualmente</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Preview */}
          {showPreview && (
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold mb-4">Preview</h3>
              <div className="space-y-4">
                {formTitle && (
                  <div>
                    <h4 className="text-xl font-bold text-gray-900">{formTitle}</h4>
                    {formDescription && (
                      <p className="text-gray-600 mt-2">{formDescription}</p>
                    )}
                  </div>
                )}
                
                {fields.map((field) => (
                  <div key={field.id}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </label>
                    {field.type === "TEXTAREA" ? (
                      <textarea
                        placeholder={field.placeholder}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        rows={3}
                        disabled
                      />
                    ) : field.type === "SELECT" ? (
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-md" disabled>
                        <option>{field.placeholder || "Selecione uma opção"}</option>
                      </select>
                    ) : (
                      <input
                        type={field.type === "EMAIL" ? "email" : field.type === "NUMBER" ? "number" : "text"}
                        placeholder={field.placeholder}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        disabled
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
