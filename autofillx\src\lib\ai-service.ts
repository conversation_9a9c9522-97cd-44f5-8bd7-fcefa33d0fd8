import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface FormSuggestion {
  label: string
  type: string
  placeholder?: string
  required: boolean
  options?: string[]
  validation?: any
}

export interface BusinessSegment {
  name: string
  description: string
  commonFields: FormSuggestion[]
}

const businessSegments: BusinessSegment[] = [
  {
    name: "concessionaria",
    description: "Concessionárias de veículos",
    commonFields: [
      { label: "Nome completo", type: "TEXT", placeholder: "Digite seu nome completo", required: true },
      { label: "CPF", type: "CPF", placeholder: "000.000.000-00", required: true },
      { label: "RG", type: "TEXT", placeholder: "Digite seu RG", required: true },
      { label: "Data de nascimento", type: "DATE", required: true },
      { label: "Estado civil", type: "SELECT", options: ["Solteiro(a)", "Casado(a)", "Divorciado(a)", "Viúvo(a)"], required: true },
      { label: "Renda mensal", type: "NUMBER", placeholder: "Digite sua renda mensal", required: true },
      { label: "Profissão", type: "TEXT", placeholder: "Digite sua profissão", required: true },
      { label: "Telefone", type: "PHONE", placeholder: "(11) 99999-9999", required: true },
      { label: "Email", type: "EMAIL", placeholder: "<EMAIL>", required: true },
      { label: "CEP", type: "TEXT", placeholder: "00000-000", required: true },
      { label: "Endereço completo", type: "TEXTAREA", placeholder: "Digite seu endereço completo", required: true },
      { label: "Possui financiamento em andamento?", type: "RADIO", options: ["Sim", "Não"], required: true },
      { label: "Valor de entrada disponível", type: "NUMBER", placeholder: "Digite o valor disponível", required: false },
    ]
  },
  {
    name: "imobiliaria",
    description: "Imobiliárias e corretores",
    commonFields: [
      { label: "Nome completo", type: "TEXT", placeholder: "Digite seu nome completo", required: true },
      { label: "CPF", type: "CPF", placeholder: "000.000.000-00", required: true },
      { label: "RG", type: "TEXT", placeholder: "Digite seu RG", required: true },
      { label: "Data de nascimento", type: "DATE", required: true },
      { label: "Estado civil", type: "SELECT", options: ["Solteiro(a)", "Casado(a)", "Divorciado(a)", "Viúvo(a)"], required: true },
      { label: "Renda familiar", type: "NUMBER", placeholder: "Digite a renda familiar", required: true },
      { label: "Profissão", type: "TEXT", placeholder: "Digite sua profissão", required: true },
      { label: "Telefone", type: "PHONE", placeholder: "(11) 99999-9999", required: true },
      { label: "Email", type: "EMAIL", placeholder: "<EMAIL>", required: true },
      { label: "Endereço atual", type: "TEXTAREA", placeholder: "Digite seu endereço atual", required: true },
      { label: "Tipo de imóvel desejado", type: "SELECT", options: ["Apartamento", "Casa", "Terreno", "Comercial"], required: true },
      { label: "Valor máximo do imóvel", type: "NUMBER", placeholder: "Digite o valor máximo", required: true },
      { label: "Possui FGTS para usar?", type: "RADIO", options: ["Sim", "Não"], required: true },
      { label: "Valor disponível para entrada", type: "NUMBER", placeholder: "Digite o valor disponível", required: false },
    ]
  },
  {
    name: "clinica",
    description: "Clínicas médicas e odontológicas",
    commonFields: [
      { label: "Nome completo", type: "TEXT", placeholder: "Digite seu nome completo", required: true },
      { label: "CPF", type: "CPF", placeholder: "000.000.000-00", required: true },
      { label: "Data de nascimento", type: "DATE", required: true },
      { label: "Telefone", type: "PHONE", placeholder: "(11) 99999-9999", required: true },
      { label: "Email", type: "EMAIL", placeholder: "<EMAIL>", required: true },
      { label: "Endereço completo", type: "TEXTAREA", placeholder: "Digite seu endereço completo", required: true },
      { label: "Possui plano de saúde?", type: "RADIO", options: ["Sim", "Não"], required: true },
      { label: "Nome do plano de saúde", type: "TEXT", placeholder: "Digite o nome do plano", required: false },
      { label: "Número da carteirinha", type: "TEXT", placeholder: "Digite o número da carteirinha", required: false },
      { label: "Renda mensal", type: "NUMBER", placeholder: "Digite sua renda mensal", required: true },
      { label: "Profissão", type: "TEXT", placeholder: "Digite sua profissão", required: true },
      { label: "Tipo de tratamento desejado", type: "SELECT", options: ["Consulta", "Procedimento", "Cirurgia", "Emergência"], required: true },
      { label: "Valor máximo para pagamento", type: "NUMBER", placeholder: "Digite o valor máximo", required: false },
    ]
  },
  {
    name: "escola",
    description: "Escolas e faculdades",
    commonFields: [
      { label: "Nome completo do aluno", type: "TEXT", placeholder: "Digite o nome completo", required: true },
      { label: "CPF do aluno", type: "CPF", placeholder: "000.000.000-00", required: true },
      { label: "Data de nascimento", type: "DATE", required: true },
      { label: "Nome do responsável", type: "TEXT", placeholder: "Digite o nome do responsável", required: true },
      { label: "CPF do responsável", type: "CPF", placeholder: "000.000.000-00", required: true },
      { label: "Telefone", type: "PHONE", placeholder: "(11) 99999-9999", required: true },
      { label: "Email", type: "EMAIL", placeholder: "<EMAIL>", required: true },
      { label: "Endereço completo", type: "TEXTAREA", placeholder: "Digite o endereço completo", required: true },
      { label: "Renda familiar", type: "NUMBER", placeholder: "Digite a renda familiar", required: true },
      { label: "Curso desejado", type: "TEXT", placeholder: "Digite o curso desejado", required: true },
      { label: "Período", type: "SELECT", options: ["Manhã", "Tarde", "Noite", "Integral"], required: true },
      { label: "Já possui financiamento estudantil?", type: "RADIO", options: ["Sim", "Não"], required: true },
      { label: "Valor máximo da mensalidade", type: "NUMBER", placeholder: "Digite o valor máximo", required: false },
    ]
  }
]

export async function getFormSuggestions(
  businessType: string,
  customDescription?: string
): Promise<FormSuggestion[]> {
  // First, check if we have predefined suggestions for this business type
  const segment = businessSegments.find(s => s.name.toLowerCase() === businessType.toLowerCase())
  
  if (segment) {
    return segment.commonFields
  }

  // If no predefined segment, use AI to generate suggestions
  try {
    const prompt = `
    Você é um especialista em formulários para negócios. Crie uma lista de campos de formulário para um negócio do tipo: "${businessType}".
    ${customDescription ? `Descrição adicional: ${customDescription}` : ''}
    
    Retorne um JSON array com objetos contendo:
    - label: string (nome do campo em português)
    - type: string (TEXT, EMAIL, PHONE, NUMBER, TEXTAREA, SELECT, RADIO, CHECKBOX, DATE, FILE, CPF, CNPJ)
    - placeholder: string (opcional, texto de exemplo)
    - required: boolean
    - options: string[] (apenas para SELECT, RADIO, CHECKBOX)
    
    Foque em campos essenciais para processos de financiamento, crédito ou cadastro. Máximo 15 campos.
    `

    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
      max_tokens: 2000,
    })

    const content = response.choices[0]?.message?.content
    if (!content) {
      throw new Error('No response from AI')
    }

    const suggestions = JSON.parse(content) as FormSuggestion[]
    return suggestions

  } catch (error) {
    console.error('Error generating AI suggestions:', error)
    
    // Fallback to basic fields
    return [
      { label: "Nome completo", type: "TEXT", placeholder: "Digite seu nome completo", required: true },
      { label: "Email", type: "EMAIL", placeholder: "<EMAIL>", required: true },
      { label: "Telefone", type: "PHONE", placeholder: "(11) 99999-9999", required: true },
      { label: "CPF", type: "CPF", placeholder: "000.000.000-00", required: true },
      { label: "Observações", type: "TEXTAREA", placeholder: "Informações adicionais", required: false },
    ]
  }
}

export function getBusinessSegments(): BusinessSegment[] {
  return businessSegments
}
