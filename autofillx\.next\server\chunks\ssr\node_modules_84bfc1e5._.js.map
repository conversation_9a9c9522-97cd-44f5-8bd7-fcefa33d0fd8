{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/router-reducer/router-reducer-types.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport type { FetchServerResponseResult } from './fetch-server-response'\n\nexport const ACTION_REFRESH = 'refresh'\nexport const ACTION_NAVIGATE = 'navigate'\nexport const ACTION_RESTORE = 'restore'\nexport const ACTION_SERVER_PATCH = 'server-patch'\nexport const ACTION_PREFETCH = 'prefetch'\nexport const ACTION_HMR_REFRESH = 'hmr-refresh'\nexport const ACTION_SERVER_ACTION = 'server-action'\n\nexport type RouterChangeByServerResponse = ({\n  navigatedAt,\n  previousTree,\n  serverResponse,\n}: {\n  navigatedAt: number\n  previousTree: FlightRouterState\n  serverResponse: FetchServerResponseResult\n}) => void\n\nexport interface Mutable {\n  mpaNavigation?: boolean\n  patchedTree?: FlightRouterState\n  canonicalUrl?: string\n  scrollableSegments?: FlightSegmentPath[]\n  pendingPush?: boolean\n  cache?: CacheNode\n  prefetchCache?: AppRouterState['prefetchCache']\n  hashFragment?: string\n  shouldScroll?: boolean\n  preserveCustomHistoryState?: boolean\n  onlyHashChange?: boolean\n}\n\nexport interface ServerActionMutable extends Mutable {\n  inFlightServerAction?: Promise<any> | null\n}\n\n/**\n * Refresh triggers a refresh of the full page data.\n * - fetches the Flight data and fills rsc at the root of the cache.\n * - The router state is updated at the root.\n */\nexport interface RefreshAction {\n  type: typeof ACTION_REFRESH\n  origin: Location['origin']\n}\n\nexport interface HmrRefreshAction {\n  type: typeof ACTION_HMR_REFRESH\n  origin: Location['origin']\n}\n\nexport type ServerActionDispatcher = (\n  args: Omit<\n    ServerActionAction,\n    'type' | 'mutable' | 'navigate' | 'changeByServerResponse' | 'cache'\n  >\n) => void\n\nexport interface ServerActionAction {\n  type: typeof ACTION_SERVER_ACTION\n  actionId: string\n  actionArgs: any[]\n  resolve: (value: any) => void\n  reject: (reason?: any) => void\n}\n\n/**\n * Navigate triggers a navigation to the provided url. It supports two types: `push` and `replace`.\n *\n * `navigateType`:\n * - `push` - pushes a new history entry in the browser history\n * - `replace` - replaces the current history entry in the browser history\n *\n * Navigate has multiple cache heuristics:\n * - page was prefetched\n *  - Apply router state tree from prefetch\n *  - Apply Flight data from prefetch to the cache\n *  - If Flight data is a string, it's a redirect and the state is updated to trigger a redirect\n *  - Check if hard navigation is needed\n *    - Hard navigation happens when a dynamic parameter below the common layout changed\n *    - When hard navigation is needed the cache is invalidated below the flightSegmentPath\n *    - The missing cache nodes of the page will be fetched in layout-router and trigger the SERVER_PATCH action\n *  - If hard navigation is not needed\n *    - The cache is reused\n *    - If any cache nodes are missing they'll be fetched in layout-router and trigger the SERVER_PATCH action\n * - page was not prefetched\n *  - The navigate was called from `next/router` (`router.push()` / `router.replace()`) / `next/link` without prefetched data available (e.g. the prefetch didn't come back from the server before clicking the link)\n *    - Flight data is fetched in the reducer (suspends the reducer)\n *    - Router state tree is created based on Flight data\n *    - Cache is filled based on the Flight data\n *\n * Above steps explain 3 cases:\n * - `soft` - Reuses the existing cache and fetches missing nodes in layout-router.\n * - `hard` - Creates a new cache where cache nodes are removed below the common layout and fetches missing nodes in layout-router.\n * - `optimistic` (explicit no prefetch) - Creates a new cache and kicks off the data fetch in the reducer. The data fetch is awaited in the layout-router.\n */\nexport interface NavigateAction {\n  type: typeof ACTION_NAVIGATE\n  url: URL\n  isExternalUrl: boolean\n  locationSearch: Location['search']\n  navigateType: 'push' | 'replace'\n  shouldScroll: boolean\n  allowAliasing: boolean\n}\n\n/**\n * Restore applies the provided router state.\n * - Used for `popstate` (back/forward navigation) where a known router state has to be applied.\n * - Also used when syncing the router state with `pushState`/`replaceState` calls.\n * - Router state is applied as-is from the history state, if available.\n * - If the history state does not contain the router state, the existing router state is used.\n * - If any cache node is missing it will be fetched in layout-router during rendering and the server-patch case.\n * - If existing cache nodes match these are used.\n */\nexport interface RestoreAction {\n  type: typeof ACTION_RESTORE\n  url: URL\n  tree: FlightRouterState | undefined\n}\n\n/**\n * Server-patch applies the provided Flight data to the cache and router tree.\n * - Only triggered in layout-router.\n * - Creates a new cache and router state with the Flight data applied.\n */\nexport interface ServerPatchAction {\n  type: typeof ACTION_SERVER_PATCH\n  navigatedAt: number\n  serverResponse: FetchServerResponseResult\n  previousTree: FlightRouterState\n}\n\n/**\n * PrefetchKind defines the type of prefetching that should be done.\n * - `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully.\n * - `full` - prefetch the page data fully.\n * - `temporary` - a temporary prefetch entry is added to the cache, this is used when prefetch={false} is used in next/link or when you push a route programmatically.\n */\n\nexport enum PrefetchKind {\n  AUTO = 'auto',\n  FULL = 'full',\n  TEMPORARY = 'temporary',\n}\n\n/**\n * Prefetch adds the provided FlightData to the prefetch cache\n * - Creates the router state tree based on the patch in FlightData\n * - Adds the FlightData to the prefetch cache\n * - In ACTION_NAVIGATE the prefetch cache is checked and the router state tree and FlightData are applied.\n */\nexport interface PrefetchAction {\n  type: typeof ACTION_PREFETCH\n  url: URL\n  kind: PrefetchKind\n}\n\nexport interface PushRef {\n  /**\n   * If the app-router should push a new history entry in app-router's useEffect()\n   */\n  pendingPush: boolean\n  /**\n   * Multi-page navigation through location.href.\n   */\n  mpaNavigation: boolean\n  /**\n   * Skip applying the router state to the browser history state.\n   */\n  preserveCustomHistoryState: boolean\n}\n\nexport type FocusAndScrollRef = {\n  /**\n   * If focus and scroll should be set in the layout-router's useEffect()\n   */\n  apply: boolean\n  /**\n   * The hash fragment that should be scrolled to.\n   */\n  hashFragment: string | null\n  /**\n   * The paths of the segments that should be focused.\n   */\n  segmentPaths: FlightSegmentPath[]\n  /**\n   * If only the URLs hash fragment changed\n   */\n  onlyHashChange: boolean\n}\n\nexport type PrefetchCacheEntry = {\n  treeAtTimeOfPrefetch: FlightRouterState\n  data: Promise<FetchServerResponseResult>\n  kind: PrefetchKind\n  prefetchTime: number\n  staleTime: number\n  lastUsedTime: number | null\n  key: string\n  status: PrefetchCacheEntryStatus\n  url: URL\n}\n\nexport enum PrefetchCacheEntryStatus {\n  fresh = 'fresh',\n  reusable = 'reusable',\n  expired = 'expired',\n  stale = 'stale',\n}\n\n/**\n * Handles keeping the state of app-router.\n */\nexport type AppRouterState = {\n  /**\n   * The router state, this is written into the history state in app-router using replaceState/pushState.\n   * - Has to be serializable as it is written into the history state.\n   * - Holds which segments and parallel routes are shown on the screen.\n   */\n  tree: FlightRouterState\n  /**\n   * The cache holds React nodes for every segment that is shown on screen as well as previously shown segments.\n   * It also holds in-progress data requests.\n   * Prefetched data is stored separately in `prefetchCache`, that is applied during ACTION_NAVIGATE.\n   */\n  cache: CacheNode\n  /**\n   * Cache that holds prefetched Flight responses keyed by url.\n   */\n  prefetchCache: Map<string, PrefetchCacheEntry>\n  /**\n   * Decides if the update should create a new history entry and if the navigation has to trigger a browser navigation.\n   */\n  pushRef: PushRef\n  /**\n   * Decides if the update should apply scroll and focus management.\n   */\n  focusAndScrollRef: FocusAndScrollRef\n  /**\n   * The canonical url that is pushed/replaced.\n   * - This is the url you see in the browser.\n   */\n  canonicalUrl: string\n  /**\n   * The underlying \"url\" representing the UI state, which is used for intercepting routes.\n   */\n  nextUrl: string | null\n}\n\nexport type ReadonlyReducerState = Readonly<AppRouterState>\nexport type ReducerState = Promise<AppRouterState> | AppRouterState\nexport type ReducerActions = Readonly<\n  | RefreshAction\n  | NavigateAction\n  | RestoreAction\n  | ServerPatchAction\n  | PrefetchAction\n  | HmrRefreshAction\n  | ServerActionAction\n>\n"], "names": ["ACTION_REFRESH", "ACTION_NAVIGATE", "ACTION_RESTORE", "ACTION_SERVER_PATCH", "ACTION_PREFETCH", "ACTION_HMR_REFRESH", "ACTION_SERVER_ACTION", "PrefetchKind", "PrefetchCacheEntryStatus"], "mappings": ";;;;;;;;;;;AAOO,MAAMA,iBAAiB,UAAS;AAChC,MAAMC,kBAAkB,WAAU;AAClC,MAAMC,iBAAiB,UAAS;AAChC,MAAMC,sBAAsB,eAAc;AAC1C,MAAMC,kBAAkB,WAAU;AAClC,MAAMC,qBAAqB,cAAa;AACxC,MAAMC,uBAAuB,gBAAe;AAsI5C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;MAIX;AA4DM,IAAKC,2BAAAA,WAAAA,GAAAA,SAAAA,wBAAAA;;;;;WAAAA;MAKX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].ReactDOM\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].AppRouterContext\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n"], "names": ["RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_IS_PRERENDER_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;AAAO,MAAMA,aAAa,MAAc;AACjC,MAAMC,gBAAgB,cAAsB;AAI5C,MAAMC,gCAAgC,yBAAiC;AACvE,MAAMC,8BAA8B,uBAA+B;AAKnE,MAAMC,sCACX,+BAAuC;AAClC,MAAMC,0BAA0B,mBAA2B;AAC3D,MAAMC,+BAA+B,4BAAoC;AACzE,MAAMC,WAAW,WAAmB;AACpC,MAAMC,0BAA0B,mBAA2B;AAE3D,MAAMC,iBAAiB;IAC5BT;IACAE;IACAC;IACAE;IACAD;CACD,CAAS;AAEH,MAAMM,uBAAuB,OAAe;AAE5C,MAAMC,gCAAgC,sBAA8B;AACpE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,6BAA6B,0BAAkC;AACrE,MAAMC,8BAA8B,2BAAmC;AACvE,MAAMC,2BAA2B,qBAA6B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GACD;;;AAAO,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.tsx"], "sourcesContent": ["/*\n * Singleton store to track whether the app is currently being rendered\n * Used by the dev tools indicator to show render status\n */\n\nimport { useSyncExternalStore } from 'react'\n\nlet isVisible = false\nlet listeners: Array<() => void> = []\n\nconst subscribe = (listener: () => void) => {\n  listeners.push(listener)\n  return () => {\n    listeners = listeners.filter((l) => l !== listener)\n  }\n}\n\nconst getSnapshot = () => isVisible\n\nconst show = () => {\n  isVisible = true\n  listeners.forEach((listener) => listener())\n}\n\nconst hide = () => {\n  isVisible = false\n  listeners.forEach((listener) => listener())\n}\n\nexport function useIsDevRendering() {\n  return useSyncExternalStore(subscribe, getSnapshot)\n}\n\nexport const devRenderIndicator = {\n  show,\n  hide,\n}\n"], "names": ["useSyncExternalStore", "isVisible", "listeners", "subscribe", "listener", "push", "filter", "l", "getSnapshot", "show", "for<PERSON>ach", "hide", "useIsDevRendering", "devRenderIndicator"], "mappings": "AAAA;;;CAGC;;;;AAED,SAASA,oBAAoB,QAAQ,QAAO;;AAE5C,IAAIC,YAAY;AAChB,IAAIC,YAA+B,EAAE;AAErC,MAAMC,YAAY,CAACC;IACjBF,UAAUG,IAAI,CAACD;IACf,OAAO;QACLF,YAAYA,UAAUI,MAAM,CAAC,CAACC,IAAMA,MAAMH;IAC5C;AACF;AAEA,MAAMI,cAAc,IAAMP;AAE1B,MAAMQ,OAAO;IACXR,YAAY;IACZC,UAAUQ,OAAO,CAAC,CAACN,WAAaA;AAClC;AAEA,MAAMO,OAAO;IACXV,YAAY;IACZC,UAAUQ,OAAO,CAAC,CAACN,WAAaA;AAClC;AAEO,SAASQ;IACd,iNAAOZ,uBAAAA,EAAqBG,WAAWK;AACzC;AAEO,MAAMK,qBAAqB;IAChCJ;IACAE;AACF,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.tsx"], "sourcesContent": ["import { useEffect, useTransition } from 'react'\nimport { devRenderIndicator } from './dev-render-indicator'\n\nexport const useSyncDevRenderIndicator = () => {\n  const [isPending, startTransition] = useTransition()\n\n  useEffect(() => {\n    if (isPending) {\n      devRenderIndicator.show()\n    } else {\n      devRenderIndicator.hide()\n    }\n  }, [isPending])\n\n  return startTransition\n}\n"], "names": ["useEffect", "useTransition", "devRenderIndicator", "useSyncDevRenderIndicator", "isPending", "startTransition", "show", "hide"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,aAAa,QAAQ,QAAO;AAChD,SAASC,kBAAkB,QAAQ,yBAAwB;;;AAEpD,MAAMC,4BAA4B;IACvC,MAAM,CAACC,WAAWC,gBAAgB,6MAAGJ,gBAAAA;8MAErCD,YAAAA,EAAU;QACR,IAAII,WAAW;8PACbF,qBAAAA,CAAmBI,IAAI;QACzB,OAAO;8PACLJ,qBAAAA,CAAmBK,IAAI;QACzB;IACF,GAAG;QAACH;KAAU;IAEd,OAAOC;AACT,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/use-action-queue.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from './app-router-instance'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\n\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch: Dispatch<ReducerActions> | null = null\n\nexport function dispatchAppRouterAction(action: ReducerActions) {\n  if (dispatch === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  dispatch(action)\n}\n\nexport function useActionQueue(\n  actionQueue: AppRouterActionQueue\n): AppRouterState {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  // Because of a known issue that requires to decode Flight streams inside the\n  // render phase, we have to be a bit clever and assign the dispatch method to\n  // a module-level variable upon initialization. The useState hook in this\n  // module only exists to synchronize state that lives outside of React.\n  // Ideally, what we'd do instead is pass the state as a prop to root.render;\n  // this is conceptually how we're modeling the app router state, despite the\n  // weird implementation details.\n  if (process.env.NODE_ENV !== 'production') {\n    const useSyncDevRenderIndicator =\n      require('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator')\n        .useSyncDevRenderIndicator as typeof import('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator').useSyncDevRenderIndicator\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const syncDevRenderIndicator = useSyncDevRenderIndicator()\n\n    dispatch = (action: ReducerActions) => {\n      syncDevRenderIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    }\n  } else {\n    dispatch = (action: ReducerActions) =>\n      actionQueue.dispatch(action, setState)\n  }\n\n  return isThenable(state) ? use(state) : state\n}\n"], "names": ["React", "use", "isThenable", "dispatch", "dispatchAppRouterAction", "action", "Error", "useActionQueue", "actionQueue", "state", "setState", "useState", "process", "env", "NODE_ENV", "useSyncDevRenderIndicator", "require", "syncDevRenderIndicator"], "mappings": ";;;;AACA,OAAOA,SAASC,GAAG,QAAQ,QAAO;AAClC,SAASC,UAAU,QAAQ,+BAA8B;;;AAQzD,6EAA6E;AAC7E,+EAA+E;AAC/E,cAAc;AACd,IAAIC,WAA4C;AAEzC,SAASC,wBAAwBC,MAAsB;IAC5D,IAAIF,aAAa,MAAM;QACrB,MAAM,OAAA,cAEL,CAFK,IAAIG,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAH,SAASE;AACX;AAEO,SAASE,eACdC,WAAiC;IAEjC,MAAM,CAACC,OAAOC,SAAS,yMAAGV,UAAAA,CAAMW,QAAQ,CAAeH,YAAYC,KAAK;IAExE,6EAA6E;IAC7E,6EAA6E;IAC7E,yEAAyE;IACzE,uEAAuE;IACvE,4EAA4E;IAC5E,4EAA4E;IAC5E,gCAAgC;IAChC,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,MAAMC,4BACJC,QAAQ,wKACLD,yBAAyB;QAC9B,sDAAsD;QACtD,MAAME,yBAAyBF;QAE/BZ,WAAW,CAACE;YACVY,uBAAuB;gBACrBT,YAAYL,QAAQ,CAACE,QAAQK;YAC/B;QACF;IACF,OAAO;;IAGP;IAEA,kLAAOR,aAAAA,EAAWO,mNAASR,MAAAA,EAAIQ,SAASA;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/app-call-server.ts"], "sourcesContent": ["import { startTransition } from 'react'\nimport { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types'\nimport { dispatchAppRouterAction } from './components/use-action-queue'\n\nexport async function callServer(actionId: string, actionArgs: any[]) {\n  return new Promise((resolve, reject) => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_SERVER_ACTION,\n        actionId,\n        actionArgs,\n        resolve,\n        reject,\n      })\n    })\n  })\n}\n"], "names": ["startTransition", "ACTION_SERVER_ACTION", "dispatchAppRouterAction", "callServer", "actionId", "actionArgs", "Promise", "resolve", "reject", "type"], "mappings": ";;;AAAA,SAASA,eAAe,QAAQ,QAAO;AACvC,SAASC,oBAAoB,QAAQ,mDAAkD;AACvF,SAASC,uBAAuB,QAAQ,gCAA+B;;;;AAEhE,eAAeC,WAAWC,QAAgB,EAAEC,UAAiB;IAClE,OAAO,IAAIC,QAAQ,CAACC,SAASC;kNAC3BR,kBAAAA,EAAgB;sMACdE,0BAAAA,EAAwB;gBACtBO,qNAAMR,uBAAAA;gBACNG;gBACAC;gBACAE;gBACAC;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/app-find-source-map-url.ts"], "sourcesContent": ["const basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\nconst pathname = `${basePath}/__nextjs_source-map`\n\nexport const findSourceMapURL =\n  process.env.NODE_ENV === 'development'\n    ? function findSourceMapURL(filename: string): string | null {\n        if (filename === '') {\n          return null\n        }\n\n        if (\n          filename.startsWith(document.location.origin) &&\n          filename.includes('/_next/static')\n        ) {\n          // This is a request for a client chunk. This can only happen when\n          // using Turbopack. In this case, since we control how those source\n          // maps are generated, we can safely assume that the sourceMappingURL\n          // is relative to the filename, with an added `.map` extension. The\n          // browser can just request this file, and it gets served through the\n          // normal dev server, without the need to route this through\n          // the `/__nextjs_source-map` dev middleware.\n          return `${filename}.map`\n        }\n\n        const url = new URL(pathname, document.location.origin)\n        url.searchParams.set('filename', filename)\n\n        return url.href\n      }\n    : undefined\n"], "names": ["basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "pathname", "findSourceMapURL", "NODE_ENV", "filename", "startsWith", "document", "location", "origin", "includes", "url", "URL", "searchParams", "set", "href", "undefined"], "mappings": ";;;AAAA,MAAMA,WAAWC,QAAQC,GAAG,CAACC,sBAAsB,MAAI;AACvD,MAAMC,WAAY,KAAEJ,WAAS;AAEtB,MAAMK,mBACXJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,cACrB,SAASD,iBAAiBE,QAAgB;IACxC,IAAIA,aAAa,IAAI;QACnB,OAAO;IACT;IAEA,IACEA,SAASC,UAAU,CAACC,SAASC,QAAQ,CAACC,MAAM,KAC5CJ,SAASK,QAAQ,CAAC,kBAClB;QACA,kEAAkE;QAClE,mEAAmE;QACnE,qEAAqE;QACrE,mEAAmE;QACnE,qEAAqE;QACrE,4DAA4D;QAC5D,6CAA6C;QAC7C,OAAQ,KAAEL,WAAS;IACrB;IAEA,MAAMM,MAAM,IAAIC,IAAIV,UAAUK,SAASC,QAAQ,CAACC,MAAM;IACtDE,IAAIE,YAAY,CAACC,GAAG,CAAC,YAAYT;IAEjC,OAAOM,IAAII,IAAI;AACjB,IACAC,UAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/flight-data-helpers.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightData,\n  FlightDataPath,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../server/app-render/types'\nimport type { HeadData } from '../shared/lib/app-router-context.shared-runtime'\n\nexport type NormalizedFlightData = {\n  /**\n   * The full `FlightSegmentPath` inclusive of the final `Segment`\n   */\n  segmentPath: FlightSegmentPath\n  /**\n   * The `FlightSegmentPath` exclusive of the final `Segment`\n   */\n  pathToSegment: FlightSegmentPath\n  segment: Segment\n  tree: FlightRouterState\n  seedData: CacheNodeSeedData | null\n  head: HeadData\n  isHeadPartial: boolean\n  isRootRender: boolean\n}\n\n// TODO: We should only have to export `normalizeFlightData`, however because the initial flight data\n// that gets passed to `createInitialRouterState` doesn't conform to the `FlightDataPath` type (it's missing the root segment)\n// we're currently exporting it so we can use it directly. This should be fixed as part of the unification of\n// the different ways we express `FlightSegmentPath`.\nexport function getFlightDataPartsFromPath(\n  flightDataPath: FlightDataPath\n): NormalizedFlightData {\n  // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n  const flightDataPathLength = 4\n  // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n  const [tree, seedData, head, isHeadPartial] =\n    flightDataPath.slice(-flightDataPathLength)\n  // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n  const segmentPath = flightDataPath.slice(0, -flightDataPathLength)\n\n  return {\n    // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n    // to the start of the segment path in some places which makes it hard to use solely the segment path.\n    // Look for \"// TODO-APP: remove ''\" in the codebase.\n    pathToSegment: segmentPath.slice(0, -1),\n    segmentPath,\n    // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n    // in which case we default to ''.\n    segment: segmentPath[segmentPath.length - 1] ?? '',\n    tree,\n    seedData,\n    head,\n    isHeadPartial,\n    isRootRender: flightDataPath.length === flightDataPathLength,\n  }\n}\n\nexport function getNextFlightSegmentPath(\n  flightSegmentPath: FlightSegmentPath\n): FlightSegmentPath {\n  // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n  // to get the next segment path.\n  return flightSegmentPath.slice(2)\n}\n\nexport function normalizeFlightData(\n  flightData: FlightData\n): NormalizedFlightData[] | string {\n  // FlightData can be a string when the server didn't respond with a proper flight response,\n  // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n  if (typeof flightData === 'string') {\n    return flightData\n  }\n\n  return flightData.map(getFlightDataPartsFromPath)\n}\n"], "names": ["getFlightDataPartsFromPath", "flightDataPath", "flightDataPathLength", "tree", "seedData", "head", "isHeadPartial", "slice", "segmentPath", "pathToSegment", "segment", "length", "isRootRender", "getNextFlightSegmentPath", "flightSegmentPath", "normalizeFlightData", "flightData", "map"], "mappings": "AA2BA,qGAAqG;AACrG,8HAA8H;AAC9H,6GAA6G;AAC7G,qDAAqD;;;;;;AAC9C,SAASA,2BACdC,cAA8B;IAE9B,wGAAwG;IACxG,MAAMC,uBAAuB;IAC7B,sFAAsF;IACtF,MAAM,CAACC,MAAMC,UAAUC,MAAMC,cAAc,GACzCL,eAAeM,KAAK,CAAC,CAACL;IACxB,6GAA6G;IAC7G,MAAMM,cAAcP,eAAeM,KAAK,CAAC,GAAG,CAACL;QAUlCM;IARX,OAAO;QACL,kGAAkG;QAClG,sGAAsG;QACtG,qDAAqD;QACrDC,eAAeD,YAAYD,KAAK,CAAC,GAAG,CAAC;QACrCC;QACA,kFAAkF;QAClF,kCAAkC;QAClCE,SAASF,CAAAA,gBAAAA,WAAW,CAACA,YAAYG,MAAM,GAAG,EAAE,KAAA,OAAnCH,gBAAuC;QAChDL;QACAC;QACAC;QACAC;QACAM,cAAcX,eAAeU,MAAM,KAAKT;IAC1C;AACF;AAEO,SAASW,yBACdC,iBAAoC;IAEpC,4GAA4G;IAC5G,gCAAgC;IAChC,OAAOA,kBAAkBP,KAAK,CAAC;AACjC;AAEO,SAASQ,oBACdC,UAAsB;IAEtB,2FAA2F;IAC3F,kGAAkG;IAClG,IAAI,OAAOA,eAAe,UAAU;QAClC,OAAOA;IACT;IAEA,OAAOA,WAAWC,GAAG,CAACjB;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/app-build-id.ts"], "sourcesContent": ["// This gets assigned as a side-effect during app initialization. Because it\n// represents the build used to create the JS bundle, it should never change\n// after being set, so we store it in a global variable.\n//\n// When performing RSC requests, if the incoming data has a different build ID,\n// we perform an MPA navigation/refresh to load the updated build and ensure\n// that the client and server in sync.\n\n// Starts as an empty string. In practice, because setAppBuildId is called\n// during initialization before hydration starts, this will always get\n// reassigned to the actual build ID before it's ever needed by a navigation.\n// If for some reasons it didn't, due to a bug or race condition, then on\n// navigation the build comparision would fail and trigger an MPA navigation.\nlet globalBuildId: string = ''\n\nexport function setAppBuildId(buildId: string) {\n  globalBuildId = buildId\n}\n\nexport function getAppBuildId(): string {\n  return globalBuildId\n}\n"], "names": ["globalBuildId", "setAppBuildId", "buildId", "getAppBuildId"], "mappings": "AAAA,4EAA4E;AAC5E,4EAA4E;AAC5E,wDAAwD;AACxD,EAAE;AACF,+EAA+E;AAC/E,4EAA4E;AAC5E,sCAAsC;AAEtC,0EAA0E;AAC1E,sEAAsE;AACtE,6EAA6E;AAC7E,yEAAyE;AACzE,6EAA6E;;;;;AAC7E,IAAIA,gBAAwB;AAErB,SAASC,cAAcC,OAAe;IAC3CF,gBAAgBE;AAClB;AAEO,SAASC;IACd,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/hash.ts"], "sourcesContent": ["// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n"], "names": ["djb2Hash", "str", "hash", "i", "length", "char", "charCodeAt", "hexHash", "toString", "slice"], "mappings": "AAAA,wCAAwC;AACxC,4CAA4C;AAC5C,iHAAiH;AACjH,wFAAwF;AACxF,gGAAgG;AAChG,wHAAwH;AACxH,wDAAwD;;;;;AACjD,SAASA,SAASC,GAAW;IAClC,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,OAAOJ,IAAIK,UAAU,CAACH;QAC5BD,OAASA,CAAAA,QAAQ,CAAA,IAAKA,OAAOG,OAAQ;IACvC;IACA,OAAOH,SAAS;AAClB;AAEO,SAASK,QAAQN,GAAW;IACjC,OAAOD,SAASC,KAAKO,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/router-reducer/set-cache-busting-search-param.ts"], "sourcesContent": ["'use client'\nimport { hexHash } from '../../../shared/lib/hash'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  NEXT_RSC_UNION_QUERY,\n} from '../app-router-headers'\nimport type { RequestHeaders } from './fetch-server-response'\n\n/**\n * Mutates the provided URL by adding a cache-busting search parameter for CDNs that don't\n * support custom headers. This helps avoid caching conflicts by making each request unique.\n *\n * Rather than relying on the Vary header which some CDNs ignore, we append a search param\n * to create a unique URL that forces a fresh request.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * Note: This function mutates the input URL directly and does not return anything.\n *\n * TODO: Since we need to use a search param anyway, we could simplify by removing the custom\n * headers approach entirely and just use search params.\n */\nexport const setCacheBustingSearchParam = (\n  url: URL,\n  headers: RequestHeaders\n): void => {\n  const uniqueCacheKey = hexHash(\n    [\n      headers[NEXT_ROUTER_PREFETCH_HEADER] || '0',\n      headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] || '0',\n      headers[NEXT_ROUTER_STATE_TREE_HEADER],\n      headers[NEXT_URL],\n    ].join(',')\n  )\n\n  /**\n   * Note that we intentionally do not use `url.searchParams.set` here:\n   *\n   * const url = new URL('https://example.com/search?q=custom%20spacing');\n   * url.searchParams.set('_rsc', 'abc123');\n   * console.log(url.toString()); // Outputs: https://example.com/search?q=custom+spacing&_rsc=abc123\n   *                                                                             ^ <--- this is causing confusion\n   * This is in fact intended based on https://url.spec.whatwg.org/#interface-urlsearchparams, but\n   * we want to preserve the %20 as %20 if that's what the user passed in, hence the custom\n   * logic below.\n   */\n  const existingSearch = url.search\n  const rawQuery = existingSearch.startsWith('?')\n    ? existingSearch.slice(1)\n    : existingSearch\n  const pairs = rawQuery.split('&').filter(Boolean)\n  pairs.push(`${NEXT_RSC_UNION_QUERY}=${uniqueCacheKey}`)\n  url.search = pairs.length ? `?${pairs.join('&')}` : ''\n}\n"], "names": ["hexHash", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_URL", "NEXT_RSC_UNION_QUERY", "setCacheBustingSearchParam", "url", "headers", "unique<PERSON><PERSON><PERSON><PERSON>", "join", "existingSearch", "search", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "slice", "pairs", "split", "filter", "Boolean", "push", "length"], "mappings": ";;;AACA,SAASA,OAAO,QAAQ,2BAA0B;AAClD,SACEC,2BAA2B,EAC3BC,mCAAmC,EACnCC,6BAA6B,EAC7BC,QAAQ,EACRC,oBAAoB,QACf,wBAAuB;AAR9B;;;AA2BO,MAAMC,6BAA6B,CACxCC,KACAC;IAEA,MAAMC,kLAAiBT,UAAAA,EACrB;QACEQ,OAAO,yLAACP,8BAAAA,CAA4B,IAAI;QACxCO,OAAO,yLAACN,sCAAAA,CAAoC,IAAI;QAChDM,OAAO,yLAACL,gCAAAA,CAA8B;QACtCK,OAAO,yLAACJ,WAAAA,CAAS;KAClB,CAACM,IAAI,CAAC;IAGT;;;;;;;;;;GAUC,GACD,MAAMC,iBAAiBJ,IAAIK,MAAM;IACjC,MAAMC,WAAWF,eAAeG,UAAU,CAAC,OACvCH,eAAeI,KAAK,CAAC,KACrBJ;IACJ,MAAMK,QAAQH,SAASI,KAAK,CAAC,KAAKC,MAAM,CAACC;IACzCH,MAAMI,IAAI,yLAAIf,uBAAAA,GAAqB,MAAGI;IACtCF,IAAIK,MAAM,GAAGI,MAAMK,MAAM,GAAI,MAAGL,MAAMN,IAAI,CAAC,OAAS;AACtD,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client-edge.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactServerDOMTurbopackClientEdge\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMTurbopackClientEdge"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,iCAAiC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/router-reducer/fetch-server-response.ts"], "sourcesContent": ["'use client'\n\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport type {\n  FlightRouterState,\n  NavigationFlightResponse,\n} from '../../../server/app-render/types'\n\nimport type { NEXT_ROUTER_SEGMENT_PREFETCH_HEADER } from '../app-router-headers'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_URL,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n} from '../app-router-headers'\nimport { callServer } from '../../app-call-server'\nimport { findSourceMapURL } from '../../app-find-source-map-url'\nimport { PrefetchKind } from './router-reducer-types'\nimport {\n  normalizeFlightData,\n  type NormalizedFlightData,\n} from '../../flight-data-helpers'\nimport { getAppBuildId } from '../../app-build-id'\nimport { setCacheBustingSearchParam } from './set-cache-busting-search-param'\n\nexport interface FetchServerResponseOptions {\n  readonly flightRouterState: FlightRouterState\n  readonly nextUrl: string | null\n  readonly prefetchKind?: PrefetchKind\n  readonly isHmrRefresh?: boolean\n}\n\nexport type FetchServerResponseResult = {\n  flightData: NormalizedFlightData[] | string\n  canonicalUrl: URL | undefined\n  couldBeIntercepted: boolean\n  prerendered: boolean\n  postponed: boolean\n  staleTime: number\n}\n\nexport type RequestHeaders = {\n  [RSC_HEADER]?: '1'\n  [NEXT_ROUTER_STATE_TREE_HEADER]?: string\n  [NEXT_URL]?: string\n  [NEXT_ROUTER_PREFETCH_HEADER]?: '1'\n  [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]?: string\n  'x-deployment-id'?: string\n  [NEXT_HMR_REFRESH_HEADER]?: '1'\n  // A header that is only added in test mode to assert on fetch priority\n  'Next-Test-Fetch-Priority'?: RequestInit['priority']\n}\n\nexport function urlToUrlWithoutFlightMarker(url: string): URL {\n  const urlWithoutFlightParameters = new URL(url, location.origin)\n  urlWithoutFlightParameters.searchParams.delete(NEXT_RSC_UNION_QUERY)\n  if (process.env.NODE_ENV === 'production') {\n    if (\n      process.env.__NEXT_CONFIG_OUTPUT === 'export' &&\n      urlWithoutFlightParameters.pathname.endsWith('.txt')\n    ) {\n      const { pathname } = urlWithoutFlightParameters\n      const length = pathname.endsWith('/index.txt') ? 10 : 4\n      // Slice off `/index.txt` or `.txt` from the end of the pathname\n      urlWithoutFlightParameters.pathname = pathname.slice(0, -length)\n    }\n  }\n  return urlWithoutFlightParameters\n}\n\nfunction doMpaNavigation(url: string): FetchServerResponseResult {\n  return {\n    flightData: urlToUrlWithoutFlightMarker(url).toString(),\n    canonicalUrl: undefined,\n    couldBeIntercepted: false,\n    prerendered: false,\n    postponed: false,\n    staleTime: -1,\n  }\n}\n\nlet abortController = new AbortController()\n\nif (typeof window !== 'undefined') {\n  // Abort any in-flight requests when the page is unloaded, e.g. due to\n  // reloading the page or performing hard navigations. This allows us to ignore\n  // what would otherwise be a thrown TypeError when the browser cancels the\n  // requests.\n  window.addEventListener('pagehide', () => {\n    abortController.abort()\n  })\n\n  // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n  // and the JavaScript execution context is restored by the browser.\n  window.addEventListener('pageshow', () => {\n    abortController = new AbortController()\n  })\n}\n\n/**\n * Fetch the flight data for the provided url. Takes in the current router state\n * to decide what to render server-side.\n */\nexport async function fetchServerResponse(\n  url: URL,\n  options: FetchServerResponseOptions\n): Promise<FetchServerResponseResult> {\n  const { flightRouterState, nextUrl, prefetchKind } = options\n\n  const headers: RequestHeaders = {\n    // Enable flight response\n    [RSC_HEADER]: '1',\n    // Provide the current router state\n    [NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(\n      JSON.stringify(flightRouterState)\n    ),\n  }\n\n  /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */\n  if (prefetchKind === PrefetchKind.AUTO) {\n    headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n  }\n\n  if (process.env.NODE_ENV === 'development' && options.isHmrRefresh) {\n    headers[NEXT_HMR_REFRESH_HEADER] = '1'\n  }\n\n  if (nextUrl) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  try {\n    // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n    // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n    // Otherwise, all other prefetches are sent with a \"low\" priority.\n    // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n    const fetchPriority = prefetchKind\n      ? prefetchKind === PrefetchKind.TEMPORARY\n        ? 'high'\n        : 'low'\n      : 'auto'\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        // In \"output: export\" mode, we can't rely on headers to distinguish\n        // between HTML and RSC requests. Instead, we append an extra prefix\n        // to the request.\n        url = new URL(url)\n        if (url.pathname.endsWith('/')) {\n          url.pathname += 'index.txt'\n        } else {\n          url.pathname += '.txt'\n        }\n      }\n    }\n\n    const res = await createFetch(\n      url,\n      headers,\n      fetchPriority,\n      abortController.signal\n    )\n\n    const responseUrl = urlToUrlWithoutFlightMarker(res.url)\n    const canonicalUrl = res.redirected ? responseUrl : undefined\n\n    const contentType = res.headers.get('content-type') || ''\n    const interception = !!res.headers.get('vary')?.includes(NEXT_URL)\n    const postponed = !!res.headers.get(NEXT_DID_POSTPONE_HEADER)\n    const staleTimeHeaderSeconds = res.headers.get(\n      NEXT_ROUTER_STALE_TIME_HEADER\n    )\n    const staleTime =\n      staleTimeHeaderSeconds !== null\n        ? parseInt(staleTimeHeaderSeconds, 10) * 1000\n        : -1\n    let isFlightResponse = contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        if (!isFlightResponse) {\n          isFlightResponse = contentType.startsWith('text/plain')\n        }\n      }\n    }\n\n    // If fetch returns something different than flight response handle it like a mpa navigation\n    // If the fetch was not 200, we also handle it like a mpa navigation\n    if (!isFlightResponse || !res.ok || !res.body) {\n      // in case the original URL came with a hash, preserve it before redirecting to the new URL\n      if (url.hash) {\n        responseUrl.hash = url.hash\n      }\n\n      return doMpaNavigation(responseUrl.toString())\n    }\n\n    // We may navigate to a page that requires a different Webpack runtime.\n    // In prod, every page will have the same Webpack runtime.\n    // In dev, the Webpack runtime is minimal for each page.\n    // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n    if (process.env.NODE_ENV !== 'production' && !process.env.TURBOPACK) {\n      await require('../react-dev-overlay/app/hot-reloader-client').waitForWebpackRuntimeHotUpdate()\n    }\n\n    // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n    const flightStream = postponed\n      ? createUnclosingPrefetchStream(res.body)\n      : res.body\n    const response = await (createFromNextReadableStream(\n      flightStream\n    ) as Promise<NavigationFlightResponse>)\n\n    if (getAppBuildId() !== response.b) {\n      return doMpaNavigation(res.url)\n    }\n\n    return {\n      flightData: normalizeFlightData(response.f),\n      canonicalUrl: canonicalUrl,\n      couldBeIntercepted: interception,\n      prerendered: response.S,\n      postponed,\n      staleTime,\n    }\n  } catch (err) {\n    if (!abortController.signal.aborted) {\n      console.error(\n        `Failed to fetch RSC payload for ${url}. Falling back to browser navigation.`,\n        err\n      )\n    }\n\n    // If fetch fails handle it like a mpa navigation\n    // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n    // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n    return {\n      flightData: url.toString(),\n      canonicalUrl: undefined,\n      couldBeIntercepted: false,\n      prerendered: false,\n      postponed: false,\n      staleTime: -1,\n    }\n  }\n}\n\nexport function createFetch(\n  url: URL,\n  headers: RequestHeaders,\n  fetchPriority: 'auto' | 'high' | 'low' | null,\n  signal?: AbortSignal\n) {\n  const fetchUrl = new URL(url)\n\n  // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n  // cache busting search param) from the request so they're\n  // maximally cacheable.\n  setCacheBustingSearchParam(fetchUrl, headers)\n\n  if (process.env.__NEXT_TEST_MODE && fetchPriority !== null) {\n    headers['Next-Test-Fetch-Priority'] = fetchPriority\n  }\n\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    headers['x-deployment-id'] = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  return fetch(fetchUrl, {\n    // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n    credentials: 'same-origin',\n    headers,\n    priority: fetchPriority || undefined,\n    signal,\n  })\n}\n\nexport function createFromNextReadableStream(\n  flightStream: ReadableStream<Uint8Array>\n): Promise<unknown> {\n  return createFromReadableStream(flightStream, {\n    callServer,\n    findSourceMapURL,\n  })\n}\n\nfunction createUnclosingPrefetchStream(\n  originalFlightStream: ReadableStream<Uint8Array>\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n"], "names": ["createFromReadableStream", "process", "env", "NEXT_RUNTIME", "require", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "callServer", "findSourceMapURL", "PrefetchKind", "normalizeFlightData", "getAppBuildId", "setCacheBustingSearchParam", "urlToUrlWithoutFlightMarker", "url", "urlWithoutFlightParameters", "URL", "location", "origin", "searchParams", "delete", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "length", "slice", "doMpaNavigation", "flightData", "toString", "canonicalUrl", "undefined", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "abortController", "AbortController", "window", "addEventListener", "abort", "fetchServerResponse", "options", "flightRouterState", "nextUrl", "prefetchKind", "headers", "encodeURIComponent", "JSON", "stringify", "AUTO", "isHmrRefresh", "res", "fetchPriority", "TEMPORARY", "createFetch", "signal", "responseUrl", "redirected", "contentType", "get", "interception", "includes", "staleTimeHeaderSeconds", "parseInt", "isFlightResponse", "startsWith", "ok", "body", "hash", "TURBOPACK", "waitForWebpackRuntimeHotUpdate", "flightStream", "createUnclosingPrefetchStream", "response", "createFromNextReadableStream", "b", "f", "S", "err", "aborted", "console", "error", "fetchUrl", "__NEXT_TEST_MODE", "NEXT_DEPLOYMENT_ID", "fetch", "credentials", "priority", "originalFlightStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue"], "mappings": ";;;;;;AAmBA,SACEK,2BAA2B,EAC3BC,6BAA6B,EAC7BC,oBAAoB,EACpBC,QAAQ,EACRC,UAAU,EACVC,uBAAuB,EACvBC,uBAAuB,EACvBC,wBAAwB,EACxBC,6BAA6B,QACxB,wBAAuB;AAC9B,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,YAAY,QAAQ,yBAAwB;AACrD,SACEC,mBAAmB,QAEd,4BAA2B;AAClC,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,0BAA0B,QAAQ,mCAAkC;AAtC7E;AAEA,aAAa;AACb,6DAA6D;AAC7D,6EAA6E;AAC7E,MAAM,EAAEnB,wBAAwB,EAAE,GAChC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;;;;;;;;AA0DP,SAASgB,4BAA4BC,GAAW;IACrD,MAAMC,6BAA6B,IAAIC,IAAIF,KAAKG,SAASC,MAAM;IAC/DH,2BAA2BI,YAAY,CAACC,MAAM,yLAACpB,uBAAAA;IAC/C,IAAIN,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,UAAc;;IAU3C;IACA,OAAON;AACT;AAEA,SAASY,gBAAgBb,GAAW;IAClC,OAAO;QACLc,YAAYf,4BAA4BC,KAAKe,QAAQ;QACrDC,cAAcC;QACdC,oBAAoB;QACpBC,aAAa;QACbC,WAAW;QACXC,WAAW,CAAC;IACd;AACF;AAEA,IAAIC,kBAAkB,IAAIC;AAE1B,IAAI,OAAOC,WAAW,aAAa;IACjC,sEAAsE;IACtE,8EAA8E;IAC9E,0EAA0E;IAC1E,YAAY;IACZA,OAAOC,gBAAgB,CAAC,YAAY;QAClCH,gBAAgBI,KAAK;IACvB;IAEA,8EAA8E;IAC9E,mEAAmE;IACnEF,OAAOC,gBAAgB,CAAC,YAAY;QAClCH,kBAAkB,IAAIC;IACxB;AACF;AAMO,eAAeI,oBACpB3B,GAAQ,EACR4B,OAAmC;IAEnC,MAAM,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGH;IAErD,MAAMI,UAA0B;QAC9B,yBAAyB;QACzB,yLAAC5C,aAAAA,CAAW,EAAE;QACd,mCAAmC;QACnC,yLAACH,gCAAAA,CAA8B,EAAEgD,mBAC/BC,KAAKC,SAAS,CAACN;IAEnB;IAEA;;;;;GAKC,GACD,IAAIE,gOAAiBpC,eAAAA,CAAayC,IAAI,EAAE;QACtCJ,OAAO,yLAAChD,8BAAAA,CAA4B,GAAG;IACzC;IAEA,IAAIJ,QAAQC,GAAG,CAAC0B,QAAQ,gCAAK,iBAAiBqB,QAAQS,YAAY,EAAE;QAClEL,OAAO,yLAAC1C,0BAAAA,CAAwB,GAAG;IACrC;IAEA,IAAIwC,SAAS;QACXE,OAAO,yLAAC7C,WAAAA,CAAS,GAAG2C;IACtB;IAEA,IAAI;YAoCqBQ;QAnCvB,wHAAwH;QACxH,4HAA4H;QAC5H,kEAAkE;QAClE,yHAAyH;QACzH,MAAMC,gBAAgBR,eAClBA,gOAAiBpC,eAAAA,CAAa6C,SAAS,GACrC,SACA,QACF;QAEJ,IAAI5D,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,UAAc;;QAY3C;QAEA,MAAM+B,MAAM,MAAMG,YAChBzC,KACAgC,SACAO,eACAjB,gBAAgBoB,MAAM;QAGxB,MAAMC,cAAc5C,4BAA4BuC,IAAItC,GAAG;QACvD,MAAMgB,eAAesB,IAAIM,UAAU,GAAGD,cAAc1B;QAEpD,MAAM4B,cAAcP,IAAIN,OAAO,CAACc,GAAG,CAAC,mBAAmB;QACvD,MAAMC,eAAe,CAAC,CAAA,CAAA,CAACT,mBAAAA,IAAIN,OAAO,CAACc,GAAG,CAAC,OAAA,KAAA,OAAA,KAAA,IAAhBR,iBAAyBU,QAAQ,wLAAC7D,YAAAA,CAAAA;QACzD,MAAMiC,YAAY,CAAC,CAACkB,IAAIN,OAAO,CAACc,GAAG,yLAACvD,2BAAAA;QACpC,MAAM0D,yBAAyBX,IAAIN,OAAO,CAACc,GAAG,CAC5CtD,wNAAAA;QAEF,MAAM6B,YACJ4B,2BAA2B,OACvBC,SAASD,wBAAwB,MAAM,OACvC,CAAC;QACP,IAAIE,mBAAmBN,YAAYO,UAAU,yLAAC/D,0BAAAA;QAE9C,IAAIT,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,UAAc;;QAM3C;QAEA,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAAC4C,oBAAoB,CAACb,IAAIe,EAAE,IAAI,CAACf,IAAIgB,IAAI,EAAE;YAC7C,2FAA2F;YAC3F,IAAItD,IAAIuD,IAAI,EAAE;gBACZZ,YAAYY,IAAI,GAAGvD,IAAIuD,IAAI;YAC7B;YAEA,OAAO1C,gBAAgB8B,YAAY5B,QAAQ;QAC7C;QAEA,uEAAuE;QACvE,0DAA0D;QAC1D,wDAAwD;QACxD,oGAAoG;QACpG,IAAInC,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,UAAwC,MAAxB,CAAC3B,QAAQC,GAAG,CAAC2E,SAAS;;QAEnE;QAEA,2EAA2E;QAC3E,MAAME,eAAetC,YACjBuC,8BAA8BrB,IAAIgB,IAAI,IACtChB,IAAIgB,IAAI;QACZ,MAAMM,WAAW,MAAOC,6BACtBH;QAGF,4KAAI7D,gBAAAA,QAAoB+D,SAASE,CAAC,EAAE;YAClC,OAAOjD,gBAAgByB,IAAItC,GAAG;QAChC;QAEA,OAAO;YACLc,gBAAYlB,iMAAAA,EAAoBgE,SAASG,CAAC;YAC1C/C,cAAcA;YACdE,oBAAoB6B;YACpB5B,aAAayC,SAASI,CAAC;YACvB5C;YACAC;QACF;IACF,EAAE,OAAO4C,KAAK;QACZ,IAAI,CAAC3C,gBAAgBoB,MAAM,CAACwB,OAAO,EAAE;YACnCC,QAAQC,KAAK,CACV,qCAAkCpE,MAAI,yCACvCiE;QAEJ;QAEA,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YACLnD,YAAYd,IAAIe,QAAQ;YACxBC,cAAcC;YACdC,oBAAoB;YACpBC,aAAa;YACbC,WAAW;YACXC,WAAW,CAAC;QACd;IACF;AACF;AAEO,SAASoB,YACdzC,GAAQ,EACRgC,OAAuB,EACvBO,aAA6C,EAC7CG,MAAoB;IAEpB,MAAM2B,WAAW,IAAInE,IAAIF;IAEzB,6EAA6E;IAC7E,0DAA0D;IAC1D,uBAAuB;KACvBF,+PAAAA,EAA2BuE,UAAUrC;IAErC,IAAIpD,QAAQC,GAAG,CAACyF,gBAAgB,IAAI/B,GAAwB,eAAN;;IAEtD;IAEA,IAAI3D,QAAQC,GAAG,CAAC0F,kBAAkB,KAAE;;IAEpC;IAEA,OAAOC,MAAMH,UAAU;QACrB,wFAAwF;QACxFI,aAAa;QACbzC;QACA0C,UAAUnC,iBAAiBtB;QAC3ByB;IACF;AACF;AAEO,SAASmB,6BACdH,YAAwC;IAExC,OAAO/E,yBAAyB+E,cAAc;oBAC5CjE,oLAAAA;+MACAC,mBAAAA;IACF;AACF;AAEA,SAASiE,8BACPgB,oBAAgD;IAEhD,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,MAAMC,SAASD,qBAAqBE,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMN,OAAOO,IAAI;gBACzC,IAAI,CAACF,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWI,OAAO,CAACF;oBACnB;gBACF;gBACA,qEAAqE;gBACrE,qBAAqB;gBACrB;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/unresolved-thenable.ts"], "sourcesContent": ["/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */\nexport const unresolvedThenable = {\n  then: () => {},\n} as PromiseLike<void>\n"], "names": ["unresolvedThenable", "then"], "mappings": "AAAA;;CAEC,GACD;;;AAAO,MAAMA,qBAAqB;IAChCC,MAAM,KAAO;AACf,EAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].HooksClientContext\n"], "names": ["module", "exports", "require", "vendored", "HooksClientContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/navigation-untracked.ts"], "sourcesContent": ["import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams() {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) return false\n\n    const { fallbackRouteParams } = workStore\n    if (!fallbackRouteParams || fallbackRouteParams.size === 0) return false\n\n    return true\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n"], "names": ["useContext", "PathnameContext", "hasFallbackRouteParams", "window", "workAsyncStorage", "require", "workStore", "getStore", "fallbackRouteParams", "size", "useUntrackedPathname"], "mappings": ";;;AAAA,SAASA,UAAU,QAAQ,QAAO;AAClC,SAASC,eAAe,QAAQ,uDAAsD;;;AAEtF;;;;;CAKC,GACD,SAASC;IACP,IAAI,OAAOC,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,MAAMC,YAAYF,iBAAiBG,QAAQ;QAC3C,IAAI,CAACD,WAAW,OAAO;QAEvB,MAAM,EAAEE,mBAAmB,EAAE,GAAGF;QAChC,IAAI,CAACE,uBAAuBA,oBAAoBC,IAAI,KAAK,GAAG,OAAO;QAEnE,OAAO;IACT;IAEA,OAAO;AACT;AAaO,SAASC;IACd,qEAAqE;IACrE,8EAA8E;IAC9E,8EAA8E;IAC9E,uBAAuB;IACvB,IAAIR,0BAA0B;QAC5B,OAAO;IACT;IAEA,2EAA2E;IAC3E,qDAAqD;IACrD,sDAAsD;IACtD,iNAAOF,aAAAA,kOAAWC,kBAAAA;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "HTTP_ERROR_FALLBACK_ERROR_CODE", "isHTTPAccessFallbackError", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "status"], "mappings": ";;;;;;;AAAO,MAAMA,wBAAwB;IACnCC,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB,EAAC;AAED,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACP;AAErC,MAAMQ,iCAAiC,2BAA0B;AAajE,SAASC,0BACdC,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWJ,kCACXJ,cAAcW,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASI,4BACdP,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASK,mCACdC,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;AAAO,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA;MAIX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN"], "mappings": ";;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;;AAEpD,MAAMC,sBAAsB,gBAAe;AAE3C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;MAGX;AAaM,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,wMAAcd,qBAAAA;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isHTTPAccessFallbackError", "isRedirectError", "isNextRouterError", "error"], "mappings": ";;;AAAA,SACEA,yBAAyB,QAEpB,8CAA6C;AACpD,SAASC,eAAe,QAA4B,mBAAkB;;;AAO/D,SAASC,kBACdC,KAAc;IAEd,4LAAOF,kBAAAA,EAAgBE,sOAAUH,4BAAAA,EAA0BG;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/router-reducer/create-href-from-url.ts"], "sourcesContent": ["export function createHrefFromUrl(\n  url: Pick<URL, 'pathname' | 'search' | 'hash'>,\n  includeHash: boolean = true\n): string {\n  return url.pathname + url.search + (includeHash ? url.hash : '')\n}\n"], "names": ["createHrefFromUrl", "url", "includeHash", "pathname", "search", "hash"], "mappings": ";;;AAAO,SAASA,kBACdC,GAA8C,EAC9CC,WAA2B;IAA3BA,IAAAA,gBAAAA,KAAAA,GAAAA,cAAuB;IAEvB,OAAOD,IAAIE,QAAQ,GAAGF,IAAIG,MAAM,GAAIF,CAAAA,cAAcD,IAAII,IAAI,GAAG,EAAC;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/nav-failure-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n"], "names": ["useEffect", "createHrefFromUrl", "handleHardNavError", "error", "window", "next", "__pendingUrl", "URL", "location", "href", "console", "toString", "useNavFailureHandler", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "uncaughtExceptionHandler", "evt", "reason", "addEventListener", "removeEventListener"], "mappings": ";;;;AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,iBAAiB,QAAQ,wCAAuC;;;AAElE,SAASC,mBAAmBC,KAAc;IAC/C,IACEA,SACA,OAAOC,WAAW,eAClBA,OAAOC,IAAI,CAACC,YAAY,0NACxBL,oBAAAA,EAAkB,IAAIM,IAAIH,OAAOI,QAAQ,CAACC,IAAI,6NAC5CR,oBAAAA,EAAkBG,OAAOC,IAAI,CAACC,YAAY,GAC5C;QACAI,QAAQP,KAAK,CACV,qEACDA;QAEFC,OAAOI,QAAQ,CAACC,IAAI,GAAGL,OAAOC,IAAI,CAACC,YAAY,CAACK,QAAQ;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAASC;IACd,IAAIC,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;IAuB5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\n\nconst workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    if (this.state.error) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nexport function GlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default GlobalError\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useUntrackedPathname", "isNextRouterError", "handleHardNavError", "workAsyncStorage", "window", "require", "undefined", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "getDerivedStateFromProps", "props", "state", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "previousPathname", "pathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "GlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": ";;;;;;;AAEA,OAAOA,WAAyB,QAAO;AACvC,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,kBAAkB,QAAQ,wBAAuB;AAL1D;;;;;;AAOA,MAAMC,mBACJ,OAAOC,WAAW,cAEZC,QAAQ,uKACRF,gBAAgB,GAClBG;AAEN,MAAMC,SAAS;IACbC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,UAAU;QACVC,YAAY;QACZC,YAAY;QACZC,QAAQ;IACV;AACF;AA0BA,8DAA8D;AAC9D,yDAAyD;AACzD,oCAAoC;AACpC,SAASC,eAAe,KAAyB;IAAzB,IAAA,EAAEb,KAAK,EAAkB,GAAzB;IACtB,IAAIL,kBAAkB;QACpB,MAAMmB,QAAQnB,iBAAiBoB,QAAQ;QACvC,IAAID,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAOE,YAAY,KAAA,CAAIF,SAAAA,OAAAA,KAAAA,IAAAA,MAAOG,kBAAkB,GAAE;YACpDC,QAAQlB,KAAK,CAACA;YACd,MAAMA;QACR;IACF;IAEA,OAAO;AACT;AAEO,MAAMmB,6BAA6B5B,gNAAAA,CAAM6B,SAAS;IASvD,OAAOC,yBAAyBrB,KAAY,EAAE;QAC5C,qMAAIP,oBAAAA,EAAkBO,QAAQ;YAC5B,+DAA+D;YAC/D,4GAA4G;YAC5G,MAAMA;QACR;QAEA,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOsB,yBACLC,KAAgC,EAChCC,KAAgC,EACE;QAClC,MAAM,EAAExB,KAAK,EAAE,GAAGwB;QAElB,iCAAiC;QACjC,8CAA8C;QAC9C,iDAAiD;QACjD,6CAA6C;QAC7C,IAAIC,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;QAQ5C;QAEA;;;;;KAKC,GACD,IAAIJ,MAAMM,QAAQ,KAAKL,MAAMI,gBAAgB,IAAIJ,MAAMxB,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACP4B,kBAAkBL,MAAMM,QAAQ;YAClC;QACF;QACA,OAAO;YACL7B,OAAOwB,MAAMxB,KAAK;YAClB4B,kBAAkBL,MAAMM,QAAQ;QAClC;IACF;IAMA,yIAAyI;IACzIC,SAA0B;QACxB,IAAI,IAAI,CAACN,KAAK,CAACxB,KAAK,EAAE;YACpB,OAAA,WAAA,IACE,kOAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;;sCACE,8NAAA,EAACa,gBAAAA;wBAAeb,OAAO,IAAI,CAACwB,KAAK,CAACxB,KAAK;;oBACtC,IAAI,CAACuB,KAAK,CAACQ,WAAW;oBACtB,IAAI,CAACR,KAAK,CAACS,YAAY;8PACxB,MAAA,EAACC,IAAI,CAACV,KAAK,CAACW,cAAc,EAAA;wBACxBlC,OAAO,IAAI,CAACwB,KAAK,CAACxB,KAAK;wBACvBmC,OAAO,IAAI,CAACA,KAAK;;;;QAIzB;QAEA,OAAO,IAAI,CAACZ,KAAK,CAACa,QAAQ;IAC5B;IA1EAC,YAAYd,KAAgC,CAAE;QAC5C,KAAK,CAACA,QAAAA,IAAAA,CAoDRY,KAAAA,GAAQ;YACN,IAAI,CAACG,QAAQ,CAAC;gBAAEtC,OAAO;YAAK;QAC9B;QArDE,IAAI,CAACwB,KAAK,GAAG;YAAExB,OAAO;YAAM4B,kBAAkB,IAAI,CAACL,KAAK,CAACM,QAAQ;QAAC;IACpE;AAwEF;AAKO,SAASU,YAAY,KAAyB;IAAzB,IAAA,EAAEvC,KAAK,EAAkB,GAAzB;IAC1B,MAAMwC,SAA6BxC,SAAAA,OAAAA,KAAAA,IAAAA,MAAOwC,MAAM;IAChD,OAAA,WAAA,+NACE,OAAA,EAACC,QAAAA;QAAKC,IAAG;;8BACP,8NAAA,EAACC,QAAAA,CAAAA;sPACD,OAAA,EAACC,QAAAA;;8PACC,MAAA,EAAC/B,gBAAAA;wBAAeb,OAAOA;;8PACvB,MAAA,EAAC6C,OAAAA;wBAAIC,OAAO/C,OAAOC,KAAK;kCACtB,WAAA,+NAAA,OAAA,EAAC6C,OAAAA;;0QACC,OAAA,EAACE,MAAAA;oCAAGD,OAAO/C,OAAOS,IAAI;;wCAAE;wCACAgC,SAAS,WAAW;wCAAS;wCACvB5C,OAAOoD,QAAQ,CAACC,QAAQ;wCAAC;wCAAU;wCAC9DT,SAAS,gBAAgB;wCAAkB;;;gCAG7CA,SAAAA,WAAAA,+NAAS,MAAA,EAACU,KAAAA;oCAAEJ,OAAO/C,OAAOS,IAAI;8CAAI,aAAUgC;qCAAgB;;;;;;;;AAMzE;uCAIeD,YAAW;AAWnB,SAASY,cAAc,KAO7B;IAP6B,IAAA,EAC5BjB,cAAc,EACdH,WAAW,EACXC,YAAY,EACZI,QAAQ,EAGT,GAP6B;IAQ5B,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,oEAAoE;IACpE,MAAMP,sMAAWrC,uBAAAA;IACjB,IAAI0C,gBAAgB;QAClB,OAAA,WAAA,+NACE,MAAA,EAACf,sBAAAA;YACCU,UAAUA;YACVK,gBAAgBA;YAChBH,aAAaA;YACbC,cAAcA;sBAEbI;;IAGP;IAEA,OAAA,WAAA,+NAAO,MAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/match-segments.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n"], "names": ["matchSegment", "existingSegment", "segment"], "mappings": ";;;AAEO,MAAMA,eAAe,CAC1BC,iBACAC;IAEA,oCAAoC;IACpC,IAAI,OAAOD,oBAAoB,UAAU;QACvC,IAAI,OAAOC,YAAY,UAAU;YAC/B,wCAAwC;YACxC,OAAOD,oBAAoBC;QAC7B;QACA,OAAO;IACT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,OAAOD,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE,IAAID,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE;AAC/E,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/router/utils/handle-smooth-scroll.ts"], "sourcesContent": ["/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function handleSmoothScroll(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n  const htmlElement = document.documentElement\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["handleSmoothScroll", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "existing", "style", "scroll<PERSON>eh<PERSON>or", "dontForceLayout", "getClientRects"], "mappings": "AAAA;;;CAGC,GACD;;;AAAO,SAASA,mBACdC,EAAc,EACdC,OAAqE;IAArEA,IAAAA,YAAAA,KAAAA,GAAAA,UAAmE,CAAC;IAEpE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IACA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,WAAWH,YAAYI,KAAK,CAACC,cAAc;IACjDL,YAAYI,KAAK,CAACC,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQQ,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFN,YAAYO,cAAc;IAC5B;IACAV;IACAG,YAAYI,KAAK,CAACC,cAAc,GAAGF;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1143, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/router-reducer/reducers/get-segment-value.ts"], "sourcesContent": ["import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n"], "names": ["getSegmentValue", "segment", "Array", "isArray"], "mappings": ";;;AAEO,SAASA,gBAAgBC,OAAgB;IAC9C,OAAOC,MAAMC,OAAO,CAACF,WAAWA,OAAO,CAAC,EAAE,GAAGA;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["isGroupSegment", "segment", "endsWith", "isParallelRouteSegment", "startsWith", "addSearchParamsIfPageSegment", "searchParams", "isPageSegment", "includes", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "JSON", "stringify", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;AAEO,SAASA,eAAeC,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASC,uBAAuBF,OAAe;IACpD,OAAOA,QAAQG,UAAU,CAAC,QAAQH,YAAY;AAChD;AAEO,SAASI,6BACdJ,OAAgB,EAChBK,YAA2D;IAE3D,MAAMC,gBAAgBN,QAAQO,QAAQ,CAACC;IAEvC,IAAIF,eAAe;QACjB,MAAMG,mBAAmBC,KAAKC,SAAS,CAACN;QACxC,OAAOI,qBAAqB,OACxBD,mBAAmB,MAAMC,mBACzBD;IACN;IAEA,OAAOR;AACT;AAEO,MAAMQ,mBAAmB,WAAU;AACnC,MAAMI,sBAAsB,cAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["RedirectStatusCode", "RedirectType", "isRedirectError", "REDIRECT_ERROR_CODE", "actionAsyncStorage", "window", "require", "undefined", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "redirect", "getStore", "isAction", "push", "replace", "permanentRedirect", "PermanentRedirect", "getURLFromRedirectError", "split", "slice", "join", "getRedirectTypeFromError", "getRedirectStatusCodeFromError", "Number", "at"], "mappings": ";;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,YAAY,EAEZC,eAAe,EACfC,mBAAmB,QACd,mBAAkB;;;AAEzB,MAAMC,qBACJ,OAAOC,WAAW,cAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAASC,iBACdC,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,uMAAiCX,qBAAAA,CAAmBY,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,uLAAMX,sBAAAA,GAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CU,MAAME,MAAM,oLAAMZ,sBAAAA,GAAoB,MAAGO,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOE;AACT;AAcO,SAASG,SACd,2BAA2B,GAC3BP,GAAW,EACXC,IAAmB;QAEVN;IAATM,QAAAA,OAAAA,OAAAA,OAASN,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,qLAC7CjB,eAAAA,CAAakB,IAAI,oLACjBlB,eAAAA,CAAamB,OAAO;IAExB,MAAMZ,iBAAiBC,KAAKC,gMAAMV,qBAAAA,CAAmBY,iBAAiB;AACxE;AAaO,SAASS,kBACd,2BAA2B,GAC3BZ,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,wLAAqBT,eAAAA,CAAamB,OAAO;IAEzC,MAAMZ,iBAAiBC,KAAKC,gMAAMV,qBAAAA,CAAmBsB,iBAAiB;AACxE;AAUO,SAASC,wBAAwBV,KAAc;IACpD,IAAI,sLAACX,kBAAAA,EAAgBW,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAASC,yBAAyBd,KAAoB;IAC3D,IAAI,sLAACX,kBAAAA,EAAgBW,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAME,MAAM,CAACS,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASI,+BAA+Bf,KAAoB;IACjE,IAAI,sLAACX,kBAAAA,EAAgBW,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOe,OAAOhB,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKM,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1249, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "notFound", "error", "Error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,6NAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1284, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "forbidden", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,6NAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "unauthorized", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,6NAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BAILOUT_TO_CSR", "BailoutToCSRError", "Error", "constructor", "reason", "digest", "isBailoutToCSRError", "err"], "mappings": "AAAA,+GAA+G;;;;;AAC/G,MAAMA,iBAAiB;AAGhB,MAAMC,0BAA0BC;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASL;IAIzB;AACF;AAGO,SAASM,oBAAoBC,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/unstable-rethrow.browser.ts"], "sourcesContent": ["import { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (isNextRouterError(error) || isBailoutToCSRError(error)) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["isBailoutToCSRError", "isNextRouterError", "unstable_rethrow", "error", "Error", "cause"], "mappings": ";;;AAAA,SAASA,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,yBAAwB;;;AAEnD,SAASC,iBAAiBC,KAAc;IAC7C,qMAAIF,oBAAAA,EAAkBE,8MAAUH,sBAAAA,EAAoBG,QAAQ;QAC1D,MAAMA;IACR;IAEA,IAAIA,iBAAiBC,SAAS,WAAWD,OAAO;QAC9CD,iBAAiBC,MAAME,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "makeHangingPromise", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;AAAO,SAASA,+BACdC,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAASC,mBACdC,MAAmB,EACnBJ,UAAkB;IAElB,IAAII,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIV,6BAA6BG;IACzD,OAAO;QACL,MAAMQ,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAId,6BAA6BG;YAEnC,IAAIY,mBAAmBX,uBAAuBY,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCT,uBAAuBe,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["REACT_POSTPONE_TYPE", "Symbol", "for", "isPostpone", "error", "$$typeof"], "mappings": ";;;AAAA,MAAMA,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASC,WAAWC,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKL;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1478, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DYNAMIC_ERROR_CODE", "DynamicServerError", "Error", "constructor", "description", "digest", "isDynamicServerError", "err"], "mappings": ";;;;AAAA,MAAMA,qBAAqB;AAEpB,MAAMC,2BAA2BC;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCL;IAIpC;AACF;AAEO,SAASM,qBAAqBC,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAIF,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOE,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["NEXT_STATIC_GEN_BAILOUT", "StaticGenBailoutError", "Error", "code", "isStaticGenBailoutError", "error"], "mappings": ";;;;AAAA,MAAMA,0BAA0B;AAEzB,MAAMC,8BAA8BC;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AAEO,SAASI,wBACdC,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMF,IAAI,KAAKH;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1522, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME"], "mappings": ";;;;;AAAO,MAAMA,yBAAyB,6BAA4B;AAC3D,MAAMC,yBAAyB,6BAA4B;AAC3D,MAAMC,uBAAuB,2BAA0B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["scheduleOnNextTick", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "scheduleImmediate", "setImmediate", "atLeastOneTask", "waitAtLeastOneReactRenderTask", "r"], "mappings": "AAGA;;;;;CAK<PERSON>,GACD;;;;;;AAAO,MAAMA,qBAAqB,CAAWC;IAC3C,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;QAEzC,OAAO;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF,EAAC;AAQM,MAAMS,oBAAoB,CAAWT;IAC1C,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACLI,aAAaV;IACf;AACF,EAAC;AAOM,SAASW;IACd,OAAO,IAAIV,QAAc,CAACC,UAAYO,kBAAkBP;AAC1D;AAWO,SAASU;IACd,IAAIR,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACL,OAAO,IAAIL,QAAQ,CAACY,IAAMH,aAAaG;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["React", "DynamicServerError", "StaticGenBailoutError", "workUnitAsyncStorage", "workAsyncStorage", "makeHangingPromise", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "scheduleOnNextTick", "hasPostpone", "unstable_postpone", "createDynamicTrackingState", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "createDynamicValidationState", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "getFirstDynamicReason", "trackingState", "expression", "markCurrentScopeAsDynamic", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "trackFallbackParamAccessed", "prerenderStore", "getStore", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "abortOnSynchronousPlatformIOAccess", "errorWithStack", "trackSynchronousPlatformIOAccessInDev", "requestStore", "prerenderPhase", "abortAndThrowOnSynchronousRequestDataAccess", "prerenderSignal", "signal", "aborted", "validating", "syncDynamicLogged", "trackSynchronousRequestDataAccessInDev", "Postpone", "assertPostpone", "createPostponeReason", "isDynamicPostpone", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "isPrerenderInterruptedError", "accessedDynamicData", "length", "consumeDynamicAccess", "serverDynamic", "clientDynamic", "formatDynamicAPIAccesses", "filter", "access", "map", "split", "slice", "line", "join", "createPostponedAbortSignal", "AbortController", "x", "createHangingInputAbortSignal", "cacheSignal", "inputReady", "then", "annotateDynamicAccess", "useDynamicRouteParams", "workStore", "isStaticGeneration", "fallbackRouteParams", "size", "use", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "hasViewportRegex", "hasOutletRegex", "trackAllowedDynamicAccess", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "throwIfDisallowedDynamic", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAUD,wFAAwF;;;;;;;;;;;;;;;;;;;;;;;;;;;AACxF,OAAOA,WAAW,QAAO;AAEzB,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,QACf,wCAAuC;AAC9C,SAASC,kBAAkB,QAAQ,sBAAqB;;;;;;;;;AAExD,MAAMC,cAAc,6MAAOV,UAAAA,CAAMW,iBAAiB,KAAK;AA2ChD,SAASC,2BACdC,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASC;IACd,OAAO;QACLC,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASC,sBACdC,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcX,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCW,gCAAkCC,UAAU;AACrD;AASO,SAASC,0BACdC,KAAgB,EAChBC,aAAuE,EACvEH,UAAkB;IAElB,IAAIG,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,mMAAI/B,wBAAAA,CACR,CAAC,MAAM,EAAE0B,MAAMM,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIG,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1CK,qBACEP,MAAMM,KAAK,EACXR,YACAG,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,8LAAIrC,qBAAAA,CACd,CAAC,MAAM,EAAE2B,MAAMM,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAE,MAAMW,uBAAuB,GAAGb;YAChCE,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;YAEnC,MAAMH;QACR,OAAO,IACLI,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBf,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAcgB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASC,2BACdlB,KAAgB,EAChBF,UAAkB;IAElB,MAAMqB,qSAAiB5C,uBAAAA,CAAqB6C,QAAQ;IACpD,IAAI,CAACD,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhEK,qBAAqBP,MAAMM,KAAK,EAAER,YAAYqB,eAAeX,eAAe;AAC9E;AAQO,SAASa,iCACdvB,UAAkB,EAClBE,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,8LAAIrC,qBAAAA,CACd,CAAC,MAAM,EAAE2B,MAAMM,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAqB,eAAeV,UAAU,GAAG;IAE5BT,MAAMW,uBAAuB,GAAGb;IAChCE,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;IAEnC,MAAMH;AACR;AASO,SAASY,gCACdC,MAAiB,EACjBtB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBf,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAcgB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASO,oCACPlB,KAAa,EACbR,UAAkB,EAClBqB,cAAoC;IAEpC,MAAMM,SAAS,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM4B,QAAQC,gCAAgCF;IAE9CN,eAAeS,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMlB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBtB,eAAe,CAAC4C,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfjB,OAAOL,gBAAgBvB,sBAAsB,GACzC,IAAI8C,QAAQlB,KAAK,GACjBzB;YACJU;QACF;IACF;AACF;AAEO,SAASkC,mCACd1B,KAAa,EACbR,UAAkB,EAClBmC,cAAqB,EACrBd,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBnB,yBAAyB,KAAK,MAAM;YACtDmB,gBAAgBrB,qBAAqB,GAAGW;YACxCU,gBAAgBnB,yBAAyB,GAAG4C;QAC9C;IACF;IACAT,oCAAoClB,OAAOR,YAAYqB;AACzD;AAEO,SAASe,sCACdC,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASC,4CACd/B,KAAa,EACbR,UAAkB,EAClBmC,cAAqB,EACrBd,cAAoC;IAEpC,MAAMmB,kBAAkBnB,eAAeS,UAAU,CAACW,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1B,MAAMhC,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBnB,yBAAyB,KAAK,MAAM;gBACtDmB,gBAAgBrB,qBAAqB,GAAGW;gBACxCU,gBAAgBnB,yBAAyB,GAAG4C;gBAC5C,IAAId,eAAesB,UAAU,KAAK,MAAM;oBACtC,2EAA2E;oBAC3E,sEAAsE;oBACtEjC,gBAAgBkC,iBAAiB,GAAG;gBACtC;YACF;QACF;QACAlB,oCAAoClB,OAAOR,YAAYqB;IACzD;IACA,MAAMQ,gCACJ,CAAC,MAAM,EAAErB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAM6C,yCACXT,sCAAqC;AAShC,SAASU,SAAS,EAAEnB,MAAM,EAAEnB,KAAK,EAAiB;IACvD,MAAMa,qSAAiB5C,uBAAAA,CAAqB6C,QAAQ;IACpD,MAAMZ,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACND,qBAAqBD,OAAOmB,QAAQjB;AACtC;AAEO,SAASD,qBACdD,KAAa,EACbR,UAAkB,EAClBU,eAA4C;IAE5CqC;IACA,IAAIrC,iBAAiB;QACnBA,gBAAgBtB,eAAe,CAAC4C,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfjB,OAAOL,gBAAgBvB,sBAAsB,GACzC,IAAI8C,QAAQlB,KAAK,GACjBzB;YACJU;QACF;IACF;0MAEA1B,UAAAA,CAAMW,iBAAiB,CAAC+D,qBAAqBxC,OAAOR;AACtD;AAEA,SAASgD,qBAAqBxC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAASiD,kBAAkBrC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAYsC,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBvC,IAAYsC,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBxB,MAAc;IAC7C,OACEA,OAAOyB,QAAQ,CACb,sEAEFzB,OAAOyB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBH,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIf,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMoB,6BAA6B;AAEnC,SAASxB,gCAAgCqB,OAAe;IACtD,MAAMtB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMiB,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BtB,MAAc0B,MAAM,GAAGD;IACzB,OAAOzB;AACT;AAMO,SAAS2B,4BACd3B,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAc0B,MAAM,KAAKD,8BAC1B,UAAUzB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASuB,oBACdpE,eAAqC;IAErC,OAAOA,gBAAgBqE,MAAM,GAAG;AAClC;AAEO,SAASC,qBACdC,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcvE,eAAe,CAAC4C,IAAI,IAAI4B,cAAcxE,eAAe;IACnE,OAAOuE,cAAcvE,eAAe;AACtC;AAEO,SAASyE,yBACdzE,eAAqC;IAErC,OAAOA,gBACJ0E,MAAM,CACL,CAACC,SACC,OAAOA,OAAOhD,KAAK,KAAK,YAAYgD,OAAOhD,KAAK,CAAC0C,MAAM,GAAG,GAE7DO,GAAG,CAAC,CAAC,EAAEhE,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLkD,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKf,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIe,KAAKf,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIe,KAAKf,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCgB,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAEpE,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASgC;IACP,IAAI,CAAC/D,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAIiD,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAASoC,2BAA2B1C,MAAc;IACvDoB;IACA,MAAMjB,aAAa,IAAIwC;IACvB,qFAAqF;IACrF,IAAI;8MACFhG,UAAAA,CAAMW,iBAAiB,CAAC0C;IAC1B,EAAE,OAAO4C,GAAY;QACnBzC,WAAWC,KAAK,CAACwC;IACnB;IACA,OAAOzC,WAAWW,MAAM;AAC1B;AAOO,SAAS+B,8BACdrE,aAAmC;IAEnC,MAAM2B,aAAa,IAAIwC;IAEvB,IAAInE,cAAcsE,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCtE,cAAcsE,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1C7C,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;oKAC1DhD,qBAAAA,EAAmB,IAAM+C,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWW,MAAM;AAC1B;AAEO,SAASmC,sBACd5E,UAAkB,EAClBqB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBtB,eAAe,CAAC4C,IAAI,CAAC;YACnCjB,OAAOL,gBAAgBvB,sBAAsB,GACzC,IAAI8C,QAAQlB,KAAK,GACjBzB;YACJU;QACF;IACF;AACF;AAEO,SAAS6E,sBAAsB7E,UAAkB;IACtD,MAAM8E,gRAAYpG,mBAAAA,CAAiB4C,QAAQ;IAE3C,IACEwD,aACAA,UAAUC,kBAAkB,IAC5BD,UAAUE,mBAAmB,IAC7BF,UAAUE,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAM9E,oSAAgB1B,uBAAAA,CAAqB6C,QAAQ;QACnD,IAAInB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvD9B,gNAAAA,CAAM4G,GAAG,oLAACvG,qBAAAA,EAAmBwB,cAAcgF,YAAY,EAAEnF;YAC3D,OAAO,IAAIG,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9BK,qBACEqE,UAAUtE,KAAK,EACfR,YACAG,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDmB,iCAAiCvB,YAAY8E,WAAW3E;YAC1D;QACF;IACF;AACF;AAEA,MAAMiF,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,kLAAE1G,yBAAAA,CAAuB,QAAQ,CAAC;AAE/C,MAAM2G,mBAAmB,IAAID,OAC3B,CAAC,UAAU,kLAAEzG,yBAAAA,CAAuB,QAAQ,CAAC;AAE/C,MAAM2G,iBAAiB,IAAIF,OAAO,CAAC,UAAU,kLAAExG,uBAAAA,CAAqB,QAAQ,CAAC;AAEtE,SAAS2G,0BACdjF,KAAa,EACbkF,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAI4B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIL,iBAAiBO,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBjG,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI6F,iBAAiBK,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBhG,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIyF,iBAAiBQ,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBlG,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLkE,cAAcpE,yBAAyB,IACvCqE,cAAcrE,yBAAyB,EACvC;QACAoG,kBAAkB/F,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAMsD,UAAU,CAAC,OAAO,EAAE1C,MAAM,+UAA+U,CAAC;QAChX,MAAMoB,QAAQiE,8BAA8B3C,SAASwC;QACrDC,kBAAkB9F,aAAa,CAACmC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASiE,8BACP3C,OAAe,EACfwC,cAAsB;IAEtB,MAAM9D,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMiB,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BtB,MAAMb,KAAK,GAAG,YAAYmC,UAAUwC;IACpC,OAAO9D;AACT;AAEO,SAASkE,yBACdtF,KAAa,EACbmF,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAImC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAItC,cAAcpE,yBAAyB,EAAE;QAC3CwG,YAAYpC,cAAcpE,yBAAyB;QACnDyG,iBAAiBrC,cAActE,qBAAqB;QACpD4G,aAAatC,cAAcf,iBAAiB,KAAK;IACnD,OAAO,IAAIgB,cAAcrE,yBAAyB,EAAE;QAClDwG,YAAYnC,cAAcrE,yBAAyB;QACnDyG,iBAAiBpC,cAAcvE,qBAAqB;QACpD4G,aAAarC,cAAchB,iBAAiB,KAAK;IACnD,OAAO;QACLmD,YAAY;QACZC,iBAAiB1G;QACjB2G,aAAa;IACf;IAEA,IAAIN,kBAAkB/F,oBAAoB,IAAImG,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQtE,KAAK,CAACmE;QAChB;QACA,wEAAwE;QACxE,MAAM,mMAAIvH,wBAAAA;IACZ;IAEA,MAAMqB,gBAAgB8F,kBAAkB9F,aAAa;IACrD,IAAIA,cAAc4D,MAAM,EAAE;QACxB,IAAK,IAAI0C,IAAI,GAAGA,IAAItG,cAAc4D,MAAM,EAAE0C,IAAK;YAC7CD,QAAQtE,KAAK,CAAC/B,aAAa,CAACsG,EAAE;QAChC;QAEA,MAAM,mMAAI3H,wBAAAA;IACZ;IAEA,IAAI,CAACmH,kBAAkBlG,mBAAmB,EAAE;QAC1C,IAAIkG,kBAAkBjG,kBAAkB,EAAE;YACxC,IAAIqG,WAAW;gBACbG,QAAQtE,KAAK,CAACmE;gBACd,MAAM,OAAA,cAEL,CAFK,mMAAIvH,wBAAAA,CACR,CAAC,OAAO,EAAEgC,MAAM,oEAAoE,EAAEwF,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,mMAAIxH,wBAAAA,CACR,CAAC,OAAO,EAAEgC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAImF,kBAAkBhG,kBAAkB,EAAE;YAC/C,IAAIoG,WAAW;gBACbG,QAAQtE,KAAK,CAACmE;gBACd,MAAM,OAAA,cAEL,CAFK,mMAAIvH,wBAAAA,CACR,CAAC,OAAO,EAAEgC,MAAM,oEAAoE,EAAEwF,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,mMAAIxH,wBAAAA,CACR,CAAC,OAAO,EAAEgC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2058, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["isHangingPromiseRejectionError", "isPostpone", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone", "isDynamicServerError", "unstable_rethrow", "error", "Error", "cause"], "mappings": ";;;AAAA,SAASA,8BAA8B,QAAQ,uCAAsC;AACrF,SAASC,UAAU,QAAQ,4CAA2C;AACtE,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,iBAAiB,QAAQ,4CAA2C;AAC7E,SAASC,oBAAoB,QAAQ,yBAAwB;;;;;;;AAEtD,SAASC,iBAAiBC,KAAc;IAC7C,qMACEJ,oBAAAA,EAAkBI,WAClBL,yNAAAA,EAAoBK,wMACpBF,uBAAAA,EAAqBE,qMACrBH,oBAAAA,EAAkBG,wMAClBN,aAAAA,EAAWM,6LACXP,iCAAAA,EAA+BO,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBC,SAAS,WAAWD,OAAO;QAC9CD,iBAAiBC,MAAME,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2087, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC,GACD;;;AAAO,MAAMA,mBACX,OAAOC,WAAW,cAEZC,QAAQ,4HACRF,gBAAgB,GAEhBE,QAAQ,6HACRF,gBAAgB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2103, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParamsError", "Error", "constructor", "ReadonlyURLSearchParams", "URLSearchParams", "append", "delete", "set", "sort", "redirect", "permanentRedirect", "RedirectType", "notFound", "forbidden", "unauthorized", "unstable_rethrow"], "mappings": "AAAA,cAAc;;;AA4Bd,SAASS,QAAQ,EAAEC,iBAAiB,QAAQ,aAAY;AACxD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,QAAQ,QAAQ,cAAa;AACtC,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,qBAAoB;AAhCrD,MAAMf,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMC,gCAAgCC;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,SAAS;QACP,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,MAAM;QACJ,MAAM,IAAIP;IACZ;IACA,wKAAwK,GACxKQ,OAAO;QACL,MAAM,IAAIR;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2158, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ServerInsertedHtml\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedHtml"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2166, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n"], "names": ["BailoutToCSRError", "workAsyncStorage", "bailoutToClientRendering", "reason", "workStore", "getStore", "forceStatic", "isStaticGeneration"], "mappings": ";;;AAAA,SAASA,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,gBAAgB,QAAQ,sDAAqD;;;AAE/E,SAASC,yBAAyBC,MAAc;IACrD,MAAMC,gRAAYH,mBAAAA,CAAiBI,QAAQ;IAE3C,IAAID,aAAAA,OAAAA,KAAAA,IAAAA,UAAWE,WAAW,EAAE;IAE5B,IAAIF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWG,kBAAkB,EAAE,MAAM,OAAA,cAA6B,CAA7B,oMAAIP,oBAAAA,CAAkBG,SAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAA4B;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["useContext", "useMemo", "AppRouterContext", "LayoutRouterContext", "SearchParamsContext", "PathnameContext", "PathParamsContext", "getSegmentValue", "PAGE_SEGMENT_KEY", "DEFAULT_SEGMENT_KEY", "ReadonlyURLSearchParams", "useDynamicRouteParams", "window", "require", "undefined", "useSearchParams", "searchParams", "readonlySearchParams", "bailoutToClientRendering", "usePathname", "ServerInsertedHTMLContext", "useServerInsertedHTML", "useRouter", "router", "Error", "useParams", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "startsWith", "push", "useSelectedLayoutSegments", "context", "parentTree", "useSelectedLayoutSegment", "selectedLayoutSegments", "length", "selectedLayoutSegment", "notFound", "forbidden", "unauthorized", "redirect", "permanentRedirect", "RedirectType", "unstable_rethrow"], "mappings": ";;;;;;;;AAGA,SAASA,UAAU,EAAEC,OAAO,QAAQ,QAAO;AAC3C,SACEC,gBAAgB,EAChBC,mBAAmB,QAEd,qDAAoD;AAC3D,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,2BAA0B;;AAChF,SAASC,uBAAuB,QAAQ,4BAA2B;AAmFnE,wBAAwB;AACxB,SACEU,yBAAyB,EACzBC,qBAAqB,QAChB,uDAAsD;;;;;;;AArF7D,MAAMV,wBACJ,OAAOC,WAAW,cAEZC,QAAQ,sHACRF,qBAAqB,GACvBG;AAuBC,SAASC;IACd,MAAMC,yNAAehB,aAAAA,kOAAWI,sBAAAA;IAEhC,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMa,iOAAuBhB,UAAAA,EAAQ;QACnC,IAAI,CAACe,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,iNAAIN,0BAAAA,CAAwBM;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOJ,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEM,wBAAwB,EAAE,GAChCL,QAAQ;QACV,mEAAmE;QACnEK,yBAAyB;IAC3B;IAEA,OAAOD;AACT;AAoBO,SAASE;IACdR,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,8EAA8E;IAC9E,0EAA0E;IAC1E,iNAAOX,aAAAA,kOAAWK,kBAAAA;AACpB;;AA2BO,SAASiB;IACd,MAAMC,UAASvB,sNAAAA,gOAAWE,mBAAAA;IAC1B,IAAIqB,WAAW,MAAM;QACnB,MAAM,OAAA,cAAwD,CAAxD,IAAIC,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IAEA,OAAOD;AACT;AAoBO,SAASE;IACdd,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,iNAAOX,aAAAA,kOAAWM,oBAAAA;AACpB;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASoB,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,UAAAA,KAAAA,GAAAA,QAAQ;IACRC,IAAAA,gBAAAA,KAAAA,GAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,KAAA,OAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,2OAAe9B,kBAAAA,EAAgB6B;IAEnC,IAAI,CAACC,gBAAgBA,aAAaC,UAAU,iKAAC9B,mBAAAA,GAAmB;QAC9D,OAAOsB;IACT;IAEAA,YAAYS,IAAI,CAACF;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASU,0BACdZ,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAM8B,WAAUzC,sNAAAA,gOAAWG,sBAAAA;IAC3B,wFAAwF;IACxF,IAAI,CAACsC,SAAS,OAAO;IAErB,OAAOf,6BAA6Be,QAAQC,UAAU,EAAEd;AAC1D;AAqBO,SAASe,yBACdf,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAMiC,yBAAyBJ,0BAA0BZ;IAEzD,IAAI,CAACgB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJlB,qBAAqB,aACjBgB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0LAA0BrC,sBAAAA,GAC7B,OACAqC;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2323, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/redirect-boundary.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect } from 'react'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useRouter } from './navigation'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { RedirectType, isRedirectError } from './redirect-error'\n\ninterface RedirectBoundaryProps {\n  router: AppRouterInstance\n  children: React.ReactNode\n}\n\nfunction HandleRedirect({\n  redirect,\n  reset,\n  redirectType,\n}: {\n  redirect: string\n  redirectType: RedirectType\n  reset: () => void\n}) {\n  const router = useRouter()\n\n  useEffect(() => {\n    React.startTransition(() => {\n      if (redirectType === RedirectType.push) {\n        router.push(redirect, {})\n      } else {\n        router.replace(redirect, {})\n      }\n      reset()\n    })\n  }, [redirect, redirectType, reset, router])\n\n  return null\n}\n\nexport class RedirectErrorBoundary extends React.Component<\n  RedirectBoundaryProps,\n  { redirect: string | null; redirectType: RedirectType | null }\n> {\n  constructor(props: RedirectBoundaryProps) {\n    super(props)\n    this.state = { redirect: null, redirectType: null }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isRedirectError(error)) {\n      const url = getURLFromRedirectError(error)\n      const redirectType = getRedirectTypeFromError(error)\n      return { redirect: url, redirectType }\n    }\n    // Re-throw if error is not for redirect\n    throw error\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    const { redirect, redirectType } = this.state\n    if (redirect !== null && redirectType !== null) {\n      return (\n        <HandleRedirect\n          redirect={redirect}\n          redirectType={redirectType}\n          reset={() => this.setState({ redirect: null })}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function RedirectBoundary({ children }: { children: React.ReactNode }) {\n  const router = useRouter()\n  return (\n    <RedirectErrorBoundary router={router}>{children}</RedirectErrorBoundary>\n  )\n}\n"], "names": ["React", "useEffect", "useRouter", "getRedirectTypeFromError", "getURLFromRedirectError", "RedirectType", "isRedirectError", "HandleRedirect", "redirect", "reset", "redirectType", "router", "startTransition", "push", "replace", "RedirectErrorBoundary", "Component", "getDerivedStateFromError", "error", "url", "render", "state", "setState", "props", "children", "constructor", "RedirectBoundary"], "mappings": ";;;;;AACA,OAAOA,SAASC,SAAS,QAAQ,QAAO;AAExC,SAASC,SAAS,QAAQ,eAAc;;AACxC,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,YAAY,EAAEC,eAAe,QAAQ,mBAAkB;AALhE;;;;;;AAYA,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,QAAQ,EACRC,KAAK,EACLC,YAAY,EAKb,GARuB;IAStB,MAAMC,sMAAST,aAAAA;8MAEfD,YAAAA,EAAU;8MACRD,UAAAA,CAAMY,eAAe,CAAC;YACpB,IAAIF,kMAAiBL,eAAAA,CAAaQ,IAAI,EAAE;gBACtCF,OAAOE,IAAI,CAACL,UAAU,CAAC;YACzB,OAAO;gBACLG,OAAOG,OAAO,CAACN,UAAU,CAAC;YAC5B;YACAC;QACF;IACF,GAAG;QAACD;QAAUE;QAAcD;QAAOE;KAAO;IAE1C,OAAO;AACT;AAEO,MAAMI,oOAA8Bf,UAAAA,CAAMgB,SAAS;IASxD,OAAOC,yBAAyBC,KAAU,EAAE;QAC1C,yLAAIZ,kBAAAA,EAAgBY,QAAQ;YAC1B,MAAMC,MAAMf,sMAAAA,EAAwBc;YACpC,MAAMR,2LAAeP,2BAAAA,EAAyBe;YAC9C,OAAO;gBAAEV,UAAUW;gBAAKT;YAAa;QACvC;QACA,wCAAwC;QACxC,MAAMQ;IACR;IAEA,yIAAyI;IACzIE,SAA0B;QACxB,MAAM,EAAEZ,QAAQ,EAAEE,YAAY,EAAE,GAAG,IAAI,CAACW,KAAK;QAC7C,IAAIb,aAAa,QAAQE,iBAAiB,MAAM;YAC9C,OAAA,WAAA,+NACE,MAAA,EAACH,gBAAAA;gBACCC,UAAUA;gBACVE,cAAcA;gBACdD,OAAO,IAAM,IAAI,CAACa,QAAQ,CAAC;wBAAEd,UAAU;oBAAK;;QAGlD;QAEA,OAAO,IAAI,CAACe,KAAK,CAACC,QAAQ;IAC5B;IA7BAC,YAAYF,KAA4B,CAAE;QACxC,KAAK,CAACA;QACN,IAAI,CAACF,KAAK,GAAG;YAAEb,UAAU;YAAME,cAAc;QAAK;IACpD;AA2BF;AAEO,SAASgB,iBAAiB,KAA2C;IAA3C,IAAA,EAAEF,QAAQ,EAAiC,GAA3C;IAC/B,MAAMb,uMAAST,YAAAA;IACf,OAAA,WAAA,+NACE,MAAA,EAACa,uBAAAA;QAAsBJ,QAAQA;kBAASa;;AAE5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": ";;;AAAA,IAAIA,WAAW,CAACC,KAAe;AAC/B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;IACzC,MAAMC,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/http-access-fallback/error-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      this.props.missingSlots.size > 0 &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      const formattedSlots = Array.from(this.props.missingSlots)\n        .sort((a, b) => a.localeCompare(b))\n        .map((slot) => `@${slot}`)\n        .join(', ')\n\n      warningMessage += 'Missing slots: ' + formattedSlots\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"boundary-next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "useUntrackedPathname", "HTTPAccessErrorStatus", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isHTTPAccessFallbackError", "warnOnce", "MissingSlotContext", "HTTPAccessFallbackErrorBoundary", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "size", "has", "warningMessage", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "getDerivedStateFromError", "error", "httpStatus", "triggeredStatus", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "undefined", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "constructor", "HTTPAccessFallbackBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;AAEA;;;;;;;;;CASC,GAED,OAAOA,SAASC,UAAU,QAAQ,QAAO;AACzC,SAASC,oBAAoB,QAAQ,0BAAyB;AAC9D,SACEC,qBAAqB,EACrBC,2BAA2B,EAC3BC,kCAAkC,EAClCC,yBAAyB,QACpB,yBAAwB;AAC/B,SAASC,QAAQ,QAAQ,sCAAqC;AAC9D,SAASC,kBAAkB,QAAQ,wDAAuD;AAtB1F;;;;;;;AA2CA,MAAMC,8OAAwCT,UAAAA,CAAMU,SAAS;IAY3DC,oBAA0B;QACxB,IACEC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzB,IAAI,CAACC,KAAK,CAACC,YAAY,IACvB,IAAI,CAACD,KAAK,CAACC,YAAY,CAACC,IAAI,GAAG,KAC/B,4EAA4E;QAC5E,CAAC,IAAI,CAACF,KAAK,CAACC,YAAY,CAACE,GAAG,CAAC,aAC7B;YACA,IAAIC,iBACF,4HACA;YAEF,MAAMC,iBAAiBC,MAAMC,IAAI,CAAC,IAAI,CAACP,KAAK,CAACC,YAAY,EACtDO,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,OAAU,MAAGA,MAClBC,IAAI,CAAC;YAERV,kBAAkB,oBAAoBC;8LAEtCb,WAAAA,EAASY;QACX;IACF;IAEA,OAAOW,yBAAyBC,KAAU,EAAE;QAC1C,gOAAIzB,4BAAAA,EAA0ByB,QAAQ;YACpC,MAAMC,iBAAa5B,sPAAAA,EAA4B2B;YAC/C,OAAO;gBACLE,iBAAiBD;YACnB;QACF;QACA,mCAAmC;QACnC,MAAMD;IACR;IAEA,OAAOG,yBACLnB,KAA2C,EAC3CoB,KAA8B,EACE;QAChC;;;;;KAKC,GACD,IAAIpB,MAAMqB,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMF,eAAe,EAAE;YACtE,OAAO;gBACLA,iBAAiBK;gBACjBD,kBAAkBtB,MAAMqB,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,iBAAiBE,MAAMF,eAAe;YACtCI,kBAAkBtB,MAAMqB,QAAQ;QAClC;IACF;IAEAG,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAAC5B,KAAK;QAClE,MAAM,EAAEkB,eAAe,EAAE,GAAG,IAAI,CAACE,KAAK;QACtC,MAAMS,kBAAkB;YACtB,yNAACzC,wBAAAA,CAAsB0C,SAAS,CAAC,EAAEL;YACnC,yNAACrC,wBAAAA,CAAsB2C,SAAS,CAAC,EAAEL;YACnC,yNAACtC,wBAAAA,CAAsB4C,YAAY,CAAC,EAAEL;QACxC;QAEA,IAAIT,iBAAiB;YACnB,MAAMe,aACJf,4OAAoB9B,wBAAAA,CAAsB0C,SAAS,IAAIL;YACzD,MAAMS,cACJhB,4OAAoB9B,wBAAAA,CAAsB2C,SAAS,IAAIL;YACzD,MAAMS,iBACJjB,4OAAoB9B,wBAAAA,CAAsB4C,YAAY,IAAIL;YAE5D,kGAAkG;YAClG,IAAI,CAAEM,CAAAA,cAAcC,eAAeC,cAAa,GAAI;gBAClD,OAAOP;YACT;YAEA,OAAA,WAAA,+NACE,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;;8PACE,MAAA,EAACQ,QAAAA;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3BzC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAA,WAAA,+NACxB,MAAA,EAACqC,QAAAA;wBACCC,MAAK;wBACLC,qOAAShD,qCAAAA,EAAmC4B;;oBAG/CW,eAAe,CAACX,gBAAgB;;;QAGvC;QAEA,OAAOU;IACT;IArGAW,YAAYvC,KAA2C,CAAE;QACvD,KAAK,CAACA;QACN,IAAI,CAACoB,KAAK,GAAG;YACXF,iBAAiBK;YACjBD,kBAAkBtB,MAAMqB,QAAQ;QAClC;IACF;AAgGF;AAEO,SAASmB,2BAA2B,KAKT;IALS,IAAA,EACzCf,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACwB,GALS;IAMzC,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,mEAAmE;IACnE,MAAMP,sMAAWlC,uBAAAA;IACjB,MAAMc,gBAAef,sNAAAA,gOAAWO,qBAAAA;IAChC,MAAMgD,mBAAmB,CAAC,CAAEhB,CAAAA,YAAYC,aAAaC,YAAW;IAEhE,IAAIc,kBAAkB;QACpB,OAAA,WAAA,GACE,kOAAA,EAAC/C,iCAAAA;YACC2B,UAAUA;YACVI,UAAUA;YACVC,WAAWA;YACXC,cAAcA;YACd1B,cAAcA;sBAEb2B;;IAGP;IAEA,OAAA,WAAA,+NAAO,MAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2560, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/router-reducer/create-router-cache-key.ts"], "sourcesContent": ["import type { Segment } from '../../../server/app-render/types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\nexport function createRouterCacheKey(\n  segment: Segment,\n  withoutSearchParameters: boolean = false\n) {\n  // if the segment is an array, it means it's a dynamic segment\n  // for example, ['lang', 'en', 'd']. We need to convert it to a string to store it as a cache node key.\n  if (Array.isArray(segment)) {\n    return `${segment[0]}|${segment[1]}|${segment[2]}`\n  }\n\n  // Page segments might have search parameters, ie __PAGE__?foo=bar\n  // When `withoutSearchParameters` is true, we only want to return the page segment\n  if (withoutSearchParameters && segment.startsWith(PAGE_SEGMENT_KEY)) {\n    return PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n"], "names": ["PAGE_SEGMENT_KEY", "createRouterCache<PERSON>ey", "segment", "withoutSearchParameters", "Array", "isArray", "startsWith"], "mappings": ";;;AACA,SAASA,gBAAgB,QAAQ,8BAA6B;;AAEvD,SAASC,qBACdC,OAAgB,EAChBC,uBAAwC;IAAxCA,IAAAA,4BAAAA,KAAAA,GAAAA,0BAAmC;IAEnC,8DAA8D;IAC9D,uGAAuG;IACvG,IAAIC,MAAMC,OAAO,CAACH,UAAU;QAC1B,OAAUA,OAAO,CAAC,EAAE,GAAC,MAAGA,OAAO,CAAC,EAAE,GAAC,MAAGA,OAAO,CAAC,EAAE;IAClD;IAEA,kEAAkE;IAClE,kFAAkF;IAClF,IAAIC,2BAA2BD,QAAQI,UAAU,iKAACN,mBAAAA,GAAmB;QACnE,uKAAOA,mBAAAA;IACT;IAEA,OAAOE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2585, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GACD;;;AAAO,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2600, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["ensureLeadingSlash", "isGroupSegment", "normalizeAppPath", "route", "split", "reduce", "pathname", "segment", "index", "segments", "length", "normalizeRscURL", "url", "replace"], "mappings": ";;;;AAAA,SAASA,kBAAkB,QAAQ,uCAAsC;AACzE,SAASC,cAAc,QAAQ,gBAAe;;;AAqBvC,SAASC,iBAAiBC,KAAa;IAC5C,8MAAOH,qBAAAA,EACLG,MAAMC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,wKAAIL,iBAAAA,EAAeM,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASC,MAAM,GAAG,GAC5B;YACA,OAAOJ;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASI,gBAAgBC,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2638, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["normalizeAppPath", "INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "extractInterceptionRouteInformation", "interceptingRoute", "marker", "interceptedRoute", "Error", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;AAAA,SAASA,gBAAgB,QAAQ,cAAa;;AAGvC,MAAMC,6BAA6B;IACxC;IACA;IACA;IACA;CACD,CAAS;AAEH,SAASC,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLL,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASC,oCAAoCP,IAAY;IAC9D,IAAIQ,mBACFC,QACAC;IAEF,KAAK,MAAMP,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCQ,SAASX,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAIK,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGV,KAAKC,KAAK,CAACQ,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BX,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAQ,gNAAoBX,mBAAAA,EAAiBW,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BX,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAU,mBAAmBF,kBAChBP,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIJ,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMK,yBAAyBP,kBAAkBP,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIL,MACP,iCAA8BX,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAU,mBAAmBK,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIH,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2728, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/router-reducer/reducers/has-interception-route-in-current-tree.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../../server/app-render/types'\nimport { isInterceptionRouteAppPath } from '../../../../shared/lib/router/utils/interception-routes'\n\nexport function hasInterceptionRouteInCurrentTree([\n  segment,\n  parallelRoutes,\n]: FlightRouterState): boolean {\n  // If we have a dynamic segment, it's marked as an interception route by the presence of the `i` suffix.\n  if (Array.isArray(segment) && (segment[2] === 'di' || segment[2] === 'ci')) {\n    return true\n  }\n\n  // If segment is not an array, apply the existing string-based check\n  if (typeof segment === 'string' && isInterceptionRouteAppPath(segment)) {\n    return true\n  }\n\n  // Iterate through parallelRoutes if they exist\n  if (parallelRoutes) {\n    for (const key in parallelRoutes) {\n      if (hasInterceptionRouteInCurrentTree(parallelRoutes[key])) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n"], "names": ["isInterceptionRouteAppPath", "hasInterceptionRouteInCurrentTree", "segment", "parallelRoutes", "Array", "isArray", "key"], "mappings": ";;;AACA,SAASA,0BAA0B,QAAQ,0DAAyD;;AAE7F,SAASC,kCAAkC,KAG9B;IAH8B,IAAA,CAChDC,SACAC,eACkB,GAH8B;IAIhD,wGAAwG;IACxG,IAAIC,MAAMC,OAAO,CAACH,YAAaA,CAAAA,OAAO,CAAC,EAAE,KAAK,QAAQA,OAAO,CAAC,EAAE,KAAK,IAAG,GAAI;QAC1E,OAAO;IACT;IAEA,oEAAoE;IACpE,IAAI,OAAOA,YAAY,aAAYF,kOAAAA,EAA2BE,UAAU;QACtE,OAAO;IACT;IAEA,+CAA+C;IAC/C,IAAIC,gBAAgB;QAClB,IAAK,MAAMG,OAAOH,eAAgB;YAChC,IAAIF,kCAAkCE,cAAc,CAACG,IAAI,GAAG;gBAC1D,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2759, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON>ache<PERSON>ode,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { handleSmoothScroll } from '../../shared/lib/router/utils/handle-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      handleSmoothScroll(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      const navigatedAt = Date.now()\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          dispatchAppRouterAction({\n            type: ACTION_SERVER_PATCH,\n            previousTree: fullTree,\n            serverResponse,\n            navigatedAt,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n\n  // Get the active segment in the tree\n  // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n  const parentTreeSegment = parentTree[0]\n  const tree = parentTree[1][parallelRouterKey]\n  const treeSegment = tree[0]\n\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const cacheKey = createRouterCacheKey(treeSegment)\n  const stateKey = createRouterCacheKey(treeSegment, true) // no search params\n\n  // Read segment path from the parallel router cache node.\n  let cacheNode = segmentMap.get(cacheKey)\n  if (cacheNode === undefined) {\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    const newLazyCacheNode: LazyCacheNode = {\n      lazyData: null,\n      rsc: null,\n      prefetchRsc: null,\n      head: null,\n      prefetchHead: null,\n      parallelRoutes: new Map(),\n      loading: null,\n      navigatedAt: -1,\n    }\n\n    // Flight data fetch kicked off during render and put into the cache.\n    cacheNode = newLazyCacheNode\n    segmentMap.set(cacheKey, newLazyCacheNode)\n  }\n\n  /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n  // TODO: The loading module data for a segment is stored on the parent, then\n  // applied to each of that parent segment's parallel route slots. In the\n  // simple case where there's only one parallel route (the `children` slot),\n  // this is no different from if the loading module data where stored on the\n  // child directly. But I'm not sure this actually makes sense when there are\n  // multiple parallel routes. It's not a huge issue because you always have\n  // the option to define a narrower loading boundary for a particular slot. But\n  // this sort of smells like an implementation accident to me.\n  const loadingModuleData = parentCacheNode.loading\n\n  return (\n    <TemplateContext.Provider\n      key={stateKey}\n      value={\n        <ScrollAndFocusHandler segmentPath={segmentPath}>\n          <ErrorBoundary\n            errorComponent={error}\n            errorStyles={errorStyles}\n            errorScripts={errorScripts}\n          >\n            <LoadingBoundary loading={loadingModuleData}>\n              <HTTPAccessFallbackBoundary\n                notFound={notFound}\n                forbidden={forbidden}\n                unauthorized={unauthorized}\n              >\n                <RedirectBoundary>\n                  <InnerLayoutRouter\n                    url={url}\n                    tree={tree}\n                    cacheNode={cacheNode}\n                    segmentPath={segmentPath}\n                  />\n                </RedirectBoundary>\n              </HTTPAccessFallbackBoundary>\n            </LoadingBoundary>\n          </ErrorBoundary>\n        </ScrollAndFocusHandler>\n      }\n    >\n      {templateStyles}\n      {templateScripts}\n      {template}\n    </TemplateContext.Provider>\n  )\n}\n"], "names": ["ACTION_SERVER_PATCH", "React", "useContext", "use", "startTransition", "Suspense", "useDeferredValue", "ReactDOM", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "fetchServerResponse", "unresolvedThenable", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "handleSmoothScroll", "RedirectBoundary", "HTTPAccessFallbackBoundary", "createRouterCache<PERSON>ey", "hasInterceptionRouteInCurrentTree", "dispatchAppRouterAction", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "process", "env", "NODE_ENV", "console", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "Error", "InnerLayoutRouter", "tree", "cacheNode", "url", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "resolvedRsc", "then", "lazyData", "refetchTree", "includeNextUrl", "navigatedAt", "Date", "now", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "type", "previousTree", "subtree", "Provider", "value", "parentTree", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "fallback", "OuterLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "treeSegment", "concat", "cache<PERSON>ey", "stateKey", "newLazyCacheNode", "head", "prefetchHead", "errorComponent"], "mappings": ";;;;AAYA,SACEA,mBAAmB,QAEd,wCAAuC;AAE9C,OAAOC,SACLC,UAAU,EACVC,GAAG,EACHC,eAAe,EACfC,QAAQ,EACRC,gBAAgB,QAEX,QAAO;AACd,OAAOC,cAAc,YAAW;AAChC,SACEC,mBAAmB,EACnBC,yBAAyB,EACzBC,eAAe,QACV,qDAAoD;AAC3D,SAASC,mBAAmB,QAAQ,yCAAwC;AAC5E,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,qDAAoD;AACvF,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,0BAA0B,QAAQ,wCAAuC;AAClF,SAASC,oBAAoB,QAAQ,2CAA0C;AAC/E,SAASC,iCAAiC,QAAQ,mEAAkE;AACpH,SAASC,uBAAuB,QAAQ,qBAAoB;AAxC5D;;;;;;;;;;;;;;;;AA0CA;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,yLAAIb,eAAAA,EAAaS,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACK,cAAc,CAACH,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMG,UAAUR,eACdS,WACAP,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBI,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLN,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBS,KAAK,CAAC,IACxBR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,MAAMS,4QACJzB,UAAAA,CACAyB,4DAA4D;AAE9D,4FAA4F;AAC5F;;CAEC,GACD,SAASC,YACPC,QAAgD;IAEhD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAE1C,uGAAuG;IACvG,kCAAkC;IAClC,MAAMC,+BACJJ,6DAA6DC,WAAW;IAC1E,OAAOG,6BAA6BF;AACtC;AAEA,MAAMG,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACC,QAAQ,CAACC,iBAAiBF,SAASG,QAAQ,GAAG;QACpE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CC,QAAQC,IAAI,CACV,4FACAR;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMS,OAAOT,QAAQU,qBAAqB;IAC1C,OAAOZ,eAAea,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBb,OAAoB,EAAEc,cAAsB;IAC1E,MAAML,OAAOT,QAAQU,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,aAAAA,KAAAA,OAAxBC,2BACA,AACAA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE,mDADmD;AAGlG;AAMA,MAAMK,mCAAmC5D,gNAAAA,CAAM6D,SAAS;IA4GtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;QAzHF,KAAA,IAAA,OAAA,IAAA,CACEN,qBAAAA,GAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAC7C,MAAM,KAAK,KAC1C,CAACwC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYrB,KAAK,CAAC,CAAC1B,SAASmD,6LAC1B7D,eAAAA,EAAaU,SAASkD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMpB,eAAeW,kBAAkBX,YAAY;gBAEnD,IAAIA,cAAc;oBAChBoB,UAAUrB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACoB,SAAS;oBACZA,UAAU3C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE2C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMxC,kBAAkBsC,SAAU;oBACtE,IAAIjC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;4BACrC+B;wBAAJ,IAAIA,CAAAA,CAAAA,yBAAAA,QAAQG,aAAa,KAAA,OAAA,KAAA,IAArBH,uBAAuBI,SAAS,MAAK,QAAQ;wBAC/C,2FAA2F;wBAC3F,yEAAyE;wBACzE,iHAAiH;wBACnH;oBACF;oBAEA,uGAAuG;oBACvG,IAAIJ,QAAQK,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAL,UAAUA,QAAQK,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7Ed,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBX,YAAY,GAAG;gBACjCW,kBAAkBK,YAAY,GAAG,EAAE;0NAEnCzD,qBAAAA,EACE;oBACE,uEAAuE;oBACvE,IAAIyC,cAAc;;wBACdoB,QAAwBM,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc1B,SAAS2B,eAAe;oBAC5C,MAAM/B,iBAAiB8B,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAIjC,uBAAuBwB,SAAwBvB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7H8B,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAAClC,uBAAuBwB,SAAwBvB,iBAAiB;wBACnE,0EAA0E;;wBACxEuB,QAAwBM,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBrB,kBAAkBqB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxErB,kBAAkBqB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BZ,QAAQa,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BnB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMqB,mNAAUzF,cAAAA,gOAAWO,4BAAAA;IAC3B,IAAI,CAACkF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIC,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,OAAA,WAAA,+NACE,MAAA,EAAC/B,4BAAAA;QACCU,aAAaA;QACbJ,mBAAmBwB,QAAQxB,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAASuB,kBAAkB,KAU1B;IAV0B,IAAA,EACzBC,IAAI,EACJvB,WAAW,EACXwB,SAAS,EACTC,GAAG,EAMJ,GAV0B;IAWzB,MAAML,oNAAUzF,aAAAA,gOAAWO,4BAAAA;IAC3B,IAAI,CAACkF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIC,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,MAAM,EAAEE,MAAMG,QAAQ,EAAE,GAAGN;IAE3B,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMO,sBACJH,UAAUI,WAAW,KAAK,OAAOJ,UAAUI,WAAW,GAAGJ,UAAUK,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,MAAMA,gNAAW9F,mBAAAA,EAAiByF,UAAUK,GAAG,EAAEF;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMG,cACJ,OAAOD,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIE,IAAI,KAAK,uNAC3DnG,MAAAA,EAAIiG,OACJA;IAEN,IAAI,CAACC,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,8CAA8C;QAC9C,IAAIE,WAAWR,UAAUQ,QAAQ;QACjC,IAAIA,aAAa,MAAM;YACrB;;OAEC,GACD,sBAAsB;YACtB,MAAMC,cAAcnF,eAAe;gBAAC;mBAAOkD;aAAY,EAAE0B;YACzD,MAAMQ,2QAAiBtF,oCAAAA,EAAkC8E;YACzD,MAAMS,cAAcC,KAAKC,GAAG;YAC5Bb,UAAUQ,QAAQ,GAAGA,YAAW5F,yOAAAA,EAC9B,IAAIkG,IAAIb,KAAKc,SAASC,MAAM,GAC5B;gBACEC,mBAAmBR;gBACnBS,SAASR,iBAAiBd,QAAQsB,OAAO,GAAG;YAC9C,GACAX,IAAI,CAAC,CAACY;gBACN9G,4NAAAA,EAAgB;8MACdgB,0BAAAA,EAAwB;wBACtB+F,MAAMnH,qOAAAA;wBACNoH,cAAcnB;wBACdiB;wBACAR;oBACF;gBACF;gBAEA,OAAOQ;YACT;YAEA,gDAAgD;sNAChD/G,MAAAA,EAAIoG;QACN;QACA,yGAAyG;QACzG,iIAAiI;kNACjIpG,MAAAA,wLAAIS,qBAAAA;IACN;IAEA,yEAAyE;IACzE,MAAMyG,UACJ,cACA,8DAD4E,oKAC5E,gOAAC7G,sBAAAA,CAAoB8G,QAAQ,EAAA;QAC3BC,OAAO;YACLC,YAAY1B;YACZ2B,iBAAiB1B;YACjB2B,mBAAmBnD;YAEnB,kDAAkD;YAClDyB,KAAKA;QACP;kBAECK;;IAGL,iFAAiF;IACjF,OAAOgB;AACT;AAEA;;;CAGC,GACD,SAASM,gBAAgB,KAMxB;IANwB,IAAA,EACvBC,OAAO,EACPtD,QAAQ,EAIT,GANwB;IAOvB,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,8BAA8B;IAC9B,IAAIuD;IACJ,IACE,OAAOD,YAAY,YACnBA,YAAY,QACZ,OAAQA,QAAgBtB,IAAI,KAAK,YACjC;QACA,MAAMwB,oBAAoBF;QAC1BC,oBAAoB1H,gNAAAA,EAAI2H;IAC1B,OAAO;QACLD,oBAAoBD;IACtB;IAEA,IAAIC,mBAAmB;QACrB,MAAME,aAAaF,iBAAiB,CAAC,EAAE;QACvC,MAAMG,gBAAgBH,iBAAiB,CAAC,EAAE;QAC1C,MAAMI,iBAAiBJ,iBAAiB,CAAC,EAAE;QAC3C,OAAA,WAAA,IACE,iOAAA,wMAACxH,WAAAA,EAAAA;YACC6H,UAAAA,WAAAA,+NACE,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;;oBACGF;oBACAC;oBACAF;;;sBAIJzD;;IAGP;IAEA,OAAA,WAAA,+NAAO,MAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;kBAAGA;;AACZ;AAMe,SAAS6D,kBAAkB,KAsBzC;IAtByC,IAAA,EACxCC,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EAYb,GAtByC;IAuBxC,MAAMlD,WAAUzF,sNAAAA,gOAAWM,sBAAAA;IAC3B,IAAI,CAACmF,SAAS;QACZ,MAAM,OAAA,cAA2D,CAA3D,IAAIC,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAM,EAAE4B,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,EAAE1B,GAAG,EAAE,GAAGL;IAEhE,6EAA6E;IAC7E,aAAa;IACb,MAAMmD,uBAAuBrB,gBAAgBsB,cAAc;IAC3D,IAAIC,aAAaF,qBAAqBG,GAAG,CAACb;IAC1C,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACY,YAAY;QACfA,aAAa,IAAIE;QACjBJ,qBAAqBK,GAAG,CAACf,mBAAmBY;IAC9C;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMI,oBAAoB5B,UAAU,CAAC,EAAE;IACvC,MAAM1B,OAAO0B,UAAU,CAAC,EAAE,CAACY,kBAAkB;IAC7C,MAAMiB,cAAcvD,IAAI,CAAC,EAAE;IAE3B,MAAMvB,cACJmD,sBAAsB,OAElB,AACA,qCAAqC,iCADiC;IAEtE;QAACU;KAAkB,GACnBV,kBAAkB4B,MAAM,CAAC;QAACF;QAAmBhB;KAAkB;IAErE,8EAA8E;IAC9E,uEAAuE;IACvE,8EAA8E;IAC9E,6EAA6E;IAC7E,0DAA0D;IAC1D,EAAE;IACF,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMmB,oOAAWrI,uBAAAA,EAAqBmI;IACtC,MAAMG,WAAWtI,gPAAAA,EAAqBmI,aAAa,MAAM,mBAAmB;;IAE5E,yDAAyD;IACzD,IAAItD,YAAYiD,WAAWC,GAAG,CAACM;IAC/B,IAAIxD,cAAcjE,WAAW;QAC3B,2EAA2E;QAC3E,sBAAsB;QACtB,MAAM2H,mBAAkC;YACtClD,UAAU;YACVH,KAAK;YACLD,aAAa;YACbuD,MAAM;YACNC,cAAc;YACdZ,gBAAgB,IAAIG;YACpBtB,SAAS;YACTlB,aAAa,CAAC;QAChB;QAEA,qEAAqE;QACrEX,YAAY0D;QACZT,WAAWG,GAAG,CAACI,UAAUE;IAC3B;IAEA;;;;;;;;EAQA,GAEA,4EAA4E;IAC5E,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,0EAA0E;IAC1E,8EAA8E;IAC9E,6DAA6D;IAC7D,MAAM5B,oBAAoBJ,gBAAgBG,OAAO;IAEjD,OAAA,WAAA,+NACE,OAAA,EAAClH,gPAAAA,CAAgB4G,QAAQ,EAAA;QAEvBC,OAAAA,WAAAA,+NACE,MAAA,EAAC7B,uBAAAA;YAAsBnB,aAAaA;sBAClC,WAAA,IAAA,iOAAA,mLAAC1D,gBAAAA,EAAAA;gBACC+I,gBAAgBvB;gBAChBC,aAAaA;gBACbC,cAAcA;0BAEd,WAAA,+NAAA,MAAA,EAACZ,iBAAAA;oBAAgBC,SAASC;8BACxB,WAAA,+NAAA,MAAA,iNAAC5G,6BAAAA,EAAAA;wBACC0H,UAAUA;wBACVC,WAAWA;wBACXC,cAAcA;kCAEd,WAAA,+NAAA,MAAA,sLAAC7H,mBAAAA,EAAAA;sCACC,WAAA,IAAA,iOAAA,EAAC6E,mBAAAA;gCACCG,KAAKA;gCACLF,MAAMA;gCACNC,WAAWA;gCACXxB,aAAaA;;;;;;;;YAS1BiE;YACAC;YACAC;;OA9BIc;AAiCX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3244, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "TemplateContext", "RenderFromTemplateContext", "children"], "mappings": ";;;;AAEA,OAAOA,SAASC,UAAU,QAAkB,QAAO;AACnD,SAASC,eAAe,QAAQ,qDAAoD;AAHpF;;;;AAKe,SAASC;IACtB,MAAMC,qNAAWH,aAAAA,gOAAWC,kBAAAA;IAC5B,OAAA,WAAA,+NAAO,MAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;kBAAGE;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3266, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;AAAO,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3281, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;AAAO,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3308, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/shared/lib/utils/reflect-utils.ts"], "sourcesContent": ["// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  // fallthrough\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  // fallthrough\n  'status',\n\n  // React introspection\n  'displayName',\n\n  // Common tested properties\n  // fallthrough\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n"], "names": ["isDefinitelyAValidIdentifier", "describeStringPropertyAccess", "target", "prop", "test", "JSON", "stringify", "describeHasCheckingStringProperty", "stringifiedProp", "wellKnownProperties", "Set"], "mappings": "AAAA,6EAA6E;AAC7E,iFAAiF;AACjF,0FAA0F;AAC1F,uFAAuF;AACvF,2DAA2D;;;;;;AAC3D,MAAMA,+BAA+B;AAE9B,SAASC,6BAA6BC,MAAc,EAAEC,IAAY;IACvE,IAAIH,6BAA6BI,IAAI,CAACD,OAAO;QAC3C,OAAQ,MAAID,SAAO,MAAGC,OAAK;IAC7B;IACA,OAAQ,MAAID,SAAO,MAAGG,KAAKC,SAAS,CAACH,QAAM;AAC7C;AAEO,SAASI,kCACdL,MAAc,EACdC,IAAY;IAEZ,MAAMK,kBAAkBH,KAAKC,SAAS,CAACH;IACvC,OAAQ,kBAAgBD,SAAO,OAAIM,kBAAgB,UAASA,kBAAgB,SAAMN,SAAO;AAC3F;AAEO,MAAMO,sBAAsB,IAAIC,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;IAEA,oBAAoB;IACpB,cAAc;IACd;IACA;IACA;IAEA,0BAA0B;IAC1B,cAAc;IACd;IAEA,sBAAsB;IACtB;IAEA,2BAA2B;IAC3B,cAAc;IACd;IACA;IACA;CACD,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3358, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/request/search-params.browser.dev.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nexport function makeUntrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A searchParam property was accessed directly with ${expression}. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction warnForSyncSpread() {\n  console.error(\n    `The keys of \\`searchParams\\` were accessed directly. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["ReflectAdapter", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "wellKnownProperties", "CachedSearchParams", "WeakMap", "makeUntrackedExoticSearchParamsWithDevWarnings", "underlyingSearchParams", "cachedSearchParams", "get", "proxiedProperties", "Set", "unproxiedProperties", "promise", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "prop", "has", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "Reflect", "expression", "warnForSyncAccess", "set", "value", "delete", "ownKeys", "warnForSyncSpread", "console", "error"], "mappings": ";;;AAEA,SAASA,cAAc,QAAQ,mDAAkD;AACjF,SACEC,4BAA4B,EAC5BC,iCAAiC,EACjCC,mBAAmB,QACd,uCAAsC;;;AAG7C,MAAMC,qBAAqB,IAAIC;AAExB,SAASC,+CACdC,sBAAoC;IAEpC,MAAMC,qBAAqBJ,mBAAmBK,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,MAAMC,UAAUC,QAAQC,OAAO,CAACR;IAEhCS,OAAOC,IAAI,CAACV,wBAAwBW,OAAO,CAAC,CAACC;QAC3C,sLAAIhB,sBAAAA,CAAoBiB,GAAG,CAACD,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEP,oBAAoBS,IAAI,CAACF;QAC3B,OAAO;YACLT,kBAAkBY,GAAG,CAACH;YACpBN,OAAe,CAACM,KAAK,GAAGZ,sBAAsB,CAACY,KAAK;QACxD;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMX,SAAS;QACxCJ,KAAIgB,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,mLAAChB,sBAAAA,CAAoBiB,GAAG,CAACD,SACxBT,CAAAA,kBAAkBU,GAAG,CAACD,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BQ,QAAQP,GAAG,CAACK,QAAQN,UAAU,KAAI,GACpC;oBACA,MAAMS,cAAa3B,oNAAAA,EAA6B,gBAAgBkB;oBAChEU,kBAAkBD;gBACpB;YACF;YACA,wMAAO5B,iBAAAA,CAAeS,GAAG,CAACgB,QAAQN,MAAMO;QAC1C;QACAI,KAAIL,MAAM,EAAEN,IAAI,EAAEY,KAAK,EAAEL,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BT,kBAAkBsB,MAAM,CAACb;YAC3B;YACA,OAAOQ,QAAQG,GAAG,CAACL,QAAQN,MAAMY,OAAOL;QAC1C;QACAN,KAAIK,MAAM,EAAEN,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,mLAAChB,sBAAAA,CAAoBiB,GAAG,CAACD,SACxBT,CAAAA,kBAAkBU,GAAG,CAACD,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BQ,QAAQP,GAAG,CAACK,QAAQN,UAAU,KAAI,GACpC;oBACA,MAAMS,cAAa1B,yNAAAA,EACjB,gBACAiB;oBAEFU,kBAAkBD;gBACpB;YACF;YACA,OAAOD,QAAQP,GAAG,CAACK,QAAQN;QAC7B;QACAc,SAAQR,MAAM;YACZS;YACA,OAAOP,QAAQM,OAAO,CAACR;QACzB;IACF;IAEArB,mBAAmB0B,GAAG,CAACvB,wBAAwBgB;IAC/C,OAAOA;AACT;AAEA,SAASM,kBAAkBD,UAAkB;IAC3CO,QAAQC,KAAK,CACV,uDAAoDR,aAAW,OAC7D,4FACA;AAEP;AAEA,SAASM;IACPC,QAAQC,KAAK,CACV,wDACE,4FACA;AAEP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3433, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/request/search-params.browser.ts"], "sourcesContent": ["export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).makeUntrackedExoticSearchParamsWithDevWarnings\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).makeUntrackedExoticSearchParams\n"], "names": ["createRenderSearchParamsFromClient", "process", "env", "NODE_ENV", "require", "makeUntrackedExoticSearchParamsWithDevWarnings", "makeUntrackedExoticSearchParams"], "mappings": ";;;AAAO,MAAMA,qCACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAEnBC,QAAQ,2HACRC,8CAA8C,GAChD,AACED,QAAQ,gCACRE,+BAA+B,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3443, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/request/params.browser.dev.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nexport function makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      warnForEnumeration(unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A param property was accessed directly with ${expression}. \\`params\\` is now a Promise and should be unwrapped with \\`React.use()\\` before accessing properties of the underlying params object. In this version of Next.js direct access to param properties is still supported to facilitate migration but in a future version you will be required to unwrap \\`params\\` with \\`React.use()\\`.`\n  )\n}\n\nfunction warnForEnumeration(missingProperties: Array<string>) {\n  if (missingProperties.length) {\n    const describedMissingProperties =\n      describeListOfPropertyNames(missingProperties)\n    console.error(\n      `params are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  } else {\n    console.error(\n      `params are being enumerated. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  }\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["ReflectAdapter", "InvariantError", "describeStringPropertyAccess", "wellKnownProperties", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "underlyingParams", "cachedParams", "get", "promise", "Promise", "resolve", "proxiedProperties", "Set", "unproxiedProperties", "Object", "keys", "for<PERSON>ach", "prop", "has", "add", "proxiedPromise", "Proxy", "target", "receiver", "expression", "warnForSyncAccess", "set", "value", "delete", "ownKeys", "warnForEnumeration", "Reflect", "console", "error", "missingProperties", "length", "describedMissingProperties", "describeListOfPropertyNames", "properties", "description", "i"], "mappings": ";;;AAEA,SAASA,cAAc,QAAQ,mDAAkD;AACjF,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,4BAA4B,EAC5BC,mBAAmB,QACd,uCAAsC;;;;AAG7C,MAAMC,eAAe,IAAIC;AAElB,SAASC,kDACdC,gBAAwB;IAExB,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAEhC,MAAMM,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CC,OAAOC,IAAI,CAACV,kBAAkBW,OAAO,CAAC,CAACC;QACrC,IAAIhB,wMAAAA,CAAoBiB,GAAG,CAACD,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLN,kBAAkBQ,GAAG,CAACF;YACpBT,OAAe,CAACS,KAAK,GAAGZ,gBAAgB,CAACY,KAAK;QAClD;IACF;IAEA,MAAMG,iBAAiB,IAAIC,MAAMb,SAAS;QACxCD,KAAIe,MAAM,EAAEL,IAAI,EAAEM,QAAQ;YACxB,IAAI,OAAON,SAAS,UAAU;gBAC5B,IACE,AACAN,kBAAkBO,GAAG,CAACD,OACtB,0CAFuE;oBAGvE,MAAMO,mMAAaxB,+BAAAA,EAA6B,UAAUiB;oBAC1DQ,kBAAkBD;gBACpB;YACF;YACA,wMAAO1B,iBAAAA,CAAeS,GAAG,CAACe,QAAQL,MAAMM;QAC1C;QACAG,KAAIJ,MAAM,EAAEL,IAAI,EAAEU,KAAK,EAAEJ,QAAQ;YAC/B,IAAI,OAAON,SAAS,UAAU;gBAC5BN,kBAAkBiB,MAAM,CAACX;YAC3B;YACA,wMAAOnB,iBAAAA,CAAe4B,GAAG,CAACJ,QAAQL,MAAMU,OAAOJ;QACjD;QACAM,SAAQP,MAAM;YACZQ,mBAAmBjB;YACnB,OAAOkB,QAAQF,OAAO,CAACP;QACzB;IACF;IAEApB,aAAawB,GAAG,CAACrB,kBAAkBe;IACnC,OAAOA;AACT;AAEA,SAASK,kBAAkBD,UAAkB;IAC3CQ,QAAQC,KAAK,CACV,iDAA8CT,aAAW;AAE9D;AAEA,SAASM,mBAAmBI,iBAAgC;IAC1D,IAAIA,kBAAkBC,MAAM,EAAE;QAC5B,MAAMC,6BACJC,4BAA4BH;QAC9BF,QAAQC,KAAK,CACV,wEAAqEG,6BAA2B,OAC9F,6EACA;IAEP,OAAO;QACLJ,QAAQC,KAAK,CACV,kCACE,6EACA;IAEP;AACF;AAEA,SAASI,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWH,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,+KAAIpC,iBAAAA,CACR,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAQ,MAAIuC,UAAU,CAAC,EAAE,GAAC;QAC5B,KAAK;YACH,OAAQ,MAAIA,UAAU,CAAC,EAAE,GAAC,YAAWA,UAAU,CAAC,EAAE,GAAC;QACrD;YAAS;gBACP,IAAIC,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,WAAWH,MAAM,GAAG,GAAGK,IAAK;oBAC9CD,eAAgB,MAAID,UAAU,CAACE,EAAE,GAAC;gBACpC;gBACAD,eAAgB,YAAUD,UAAU,CAACA,WAAWH,MAAM,GAAG,EAAE,GAAC;gBAC5D,OAAOI;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3537, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/request/params.browser.ts"], "sourcesContent": ["export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .makeDynamicallyTrackedExoticParamsWithDevWarnings\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).makeUntrackedExoticParams\n"], "names": ["createRenderParamsFromClient", "process", "env", "NODE_ENV", "require", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "makeUntrackedExoticParams"], "mappings": ";;;AAAO,MAAMA,+BACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cACpBC,QAAQ,oHACNC,iDAAiD,GACpD,AACED,QAAQ,yBACRE,yBAAyB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3547, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["React", "errorRef", "current", "cache", "fn", "logErrorOrWarn", "process", "env", "__NEXT_DYNAMIC_IO", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "createDedupedByCallsiteServerErrorLoggerDev", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": ";;;AAAA,YAAYA,WAAW,QAAO;;AAE9B,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,6MAAOH,MAAMG,EAAK,KAAK,mNACnBH,MAAMG,EAAK,GACX,CAACC,KAA+BA;AAEtC,qEAAqE;AACrE,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,iBAAiBC,QAAQC,GAAG,CAACC,iBAAiB,GAChDC,QAAQC,KAAK,gCACbD,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBT,MAC7B,AACA,CAACU,yEADyE;IAExE,IAAI;QACFR,eAAeJ,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASY,4CACdC,UAAoC;IAEpC,OAAO,SAASC,gBAAgB,GAAGC,IAAU;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIX,QAAQC,GAAG,CAACY,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DpB,eAAea;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAML,MAAMO,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBN,uBAAuBC;YACzB;QACF,OAAO;;QAEP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3599, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  workStore.invalidUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["StaticGenBailoutError", "afterTaskAsyncStorage", "throwWithStaticGenerationBailoutError", "route", "expression", "throwWithStaticGenerationBailoutErrorWithDynamicError", "throwForSearchParamsAccessInUseCache", "workStore", "error", "Error", "invalidUsageError", "isRequestAPICallableInsideAfter", "afterTaskStore", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;AAAA,SAASA,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,qBAAqB,QAAQ,kDAAiD;;;AAGhF,SAASC,sCACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,mMAAIJ,wBAAAA,CACR,CAAC,MAAM,EAAEG,MAAM,iDAAiD,EAAEC,WAAW,0HAA0H,CAAC,GADpM,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASC,sDACdF,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,mMAAIJ,wBAAAA,CACR,CAAC,MAAM,EAAEG,MAAM,4EAA4E,EAAEC,WAAW,0HAA0H,CAAC,GAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASE,qCACdC,SAAoB;IAEpB,MAAMC,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,CAAC,MAAM,EAAEF,UAAUJ,KAAK,CAAC,oVAAoV,CAAC,GADlW,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAI,UAAUG,iBAAiB,KAAKF;IAEhC,MAAMA;AACR;AAEO,SAASG;IACd,MAAMC,uSAAiBX,wBAAAA,CAAsBY,QAAQ;IACrD,OAAOD,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBE,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3642, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/server/request/search-params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  annotateDynamicAccess,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedSearchParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { searchParams: Promise<{ foo: string }> }\n *\n * export default async function Page(props: Props) {\n *  const { searchParams } = (props.searchParams as unknown as UnsafeUnwrappedSearchParams<typeof props.searchParams>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedSearchParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    // dynamicIO Prerender\n    // We're prerendering in a mode that aborts (dynamicIO) and should stall\n    // the promise to ensure the RSC side is considered dynamic\n    return makeHangingPromise(prerenderStore.renderSignal, '`searchParams`')\n  }\n  // We're prerendering in a mode that does not aborts. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve({})\n}\n\nfunction createPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  if (prerenderStore.type === 'prerender') {\n    // We are in a dynamicIO (PPR or otherwise) prerender\n    return makeAbortingExoticSearchParams(workStore.route, prerenderStore)\n  }\n\n  // The remaining cases are prerender-ppr and prerender-legacy\n  // We are in a legacy static generation and need to interrupt the prerender\n  // when search params are accessed.\n  return makeErroringExoticSearchParams(workStore, prerenderStore)\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !workStore.isPrefetchRequest\n    ) {\n      return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore\n      )\n    } else {\n      return makeUntrackedExoticSearchParams(underlyingSearchParams, workStore)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeAbortingExoticSearchParams(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            const error = createSearchAccessError(route, expression)\n            abortAndThrowOnSynchronousRequestDataAccess(\n              route,\n              expression,\n              error,\n              prerenderStore\n            )\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        const error = createSearchAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      const error = createSearchAccessError(route, expression)\n      abortAndThrowOnSynchronousRequestDataAccess(\n        route,\n        expression,\n        error,\n        prerenderStore\n      )\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringExoticSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            if (workStore.dynamicShouldError) {\n              throwWithStaticGenerationBailoutErrorWithDynamicError(\n                workStore.route,\n                expression\n              )\n            } else if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no dynamicIO)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n        return false\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      if (workStore.dynamicShouldError) {\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      } else if (prerenderStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          prerenderStore.dynamicTracking\n        )\n      } else {\n        // Legacy Prerender\n        throwToInterruptStaticGeneration(expression, workStore, prerenderStore)\n      }\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringExoticSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests throw an error. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore)\n      }\n\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      throwForSearchParamsAccessInUseCache(workStore)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (!wellKnownProperties.has(prop)) {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamicDataInDynamicRender(store, workUnitStore)\n          return underlyingSearchParams[prop]\n        },\n        set(value) {\n          Object.defineProperty(promise, prop, {\n            value,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  let promiseInitialized = false\n  const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized) {\n        if (store.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore()\n        trackDynamicDataInDynamicRender(store, workUnitStore)\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (store.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (store.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<SearchParams>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingSearchParams))\n  )\n  promise.then(() => {\n    promiseInitialized = true\n  })\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      Object.defineProperty(promise, prop, {\n        get() {\n          return proxiedUnderlying[prop]\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && store.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          syncIODev(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin or well-known property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["ReflectAdapter", "abortAndThrowOnSynchronousRequestDataAccess", "throwToInterruptStaticGeneration", "postponeWithTracking", "trackDynamicDataInDynamicRender", "annotateDynamicAccess", "trackSynchronousRequestDataAccessInDev", "workUnitAsyncStorage", "InvariantError", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "wellKnownProperties", "throwWithStaticGenerationBailoutErrorWithDynamicError", "throwForSearchParamsAccessInUseCache", "scheduleImmediate", "createSearchParamsFromClient", "underlyingSearchParams", "workStore", "workUnitStore", "getStore", "type", "createPrerenderSearchParams", "createRenderSearchParams", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "forceStatic", "Promise", "resolve", "prerenderStore", "renderSignal", "makeAbortingExoticSearchParams", "route", "makeErroringExoticSearchParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeDynamicallyTrackedExoticSearchParamsWithDevWarnings", "makeUntrackedExoticSearchParams", "CachedSearchParams", "WeakMap", "CachedSearchParamsForUseCache", "cachedSearchParams", "get", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "Object", "hasOwn", "expression", "has", "error", "createSearchAccessError", "ownKeys", "set", "dynamicShouldError", "dynamicTracking", "makeErroringExoticSearchParamsForUseCache", "store", "keys", "for<PERSON>ach", "defineProperty", "value", "writable", "enumerable", "configurable", "proxiedProperties", "Set", "unproxiedProperties", "promiseInitialized", "proxiedUnderlying", "Reflect", "then", "push", "add", "newValue", "syncIODev", "delete", "missingProperties", "length", "warnForIncompleteEnumeration", "warnForSyncAccess", "prerenderPhase", "requestStore", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "description", "i"], "mappings": ";;;;;;;AAEA,SAASA,cAAc,QAAQ,yCAAwC;AACvE,SACEC,2CAA2C,EAC3CC,gCAAgC,EAChCC,oBAAoB,EACpBC,+BAA+B,EAC/BC,qBAAqB,EACrBC,sCAAsC,QACjC,kCAAiC;AAExC,SACEC,oBAAoB,QAKf,iDAAgD;AACvD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SACEC,4BAA4B,EAC5BC,iCAAiC,EACjCC,mBAAmB,QACd,uCAAsC;AAC7C,SACEC,qDAAqD,EACrDC,oCAAoC,QAC/B,UAAS;AAChB,SAASC,iBAAiB,QAAQ,sBAAqB;;;;;;;;;;AAgChD,SAASC,6BACdC,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,mSAAgBb,wBAAAA,CAAqBc,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BAA4BJ,WAAWC;YAChD;QAEF;IACF;IACA,OAAOI,yBAAyBN,wBAAwBC;AAC1D;AAGO,MAAMM,sCACXC,sCAAqC;AAEhC,SAASA,sCACdR,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,oSAAgBb,uBAAAA,CAAqBc,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BAA4BJ,WAAWC;YAChD;QAEF;IACF;IACA,OAAOI,yBAAyBN,wBAAwBC;AAC1D;AAEO,SAASQ,yCACdR,SAAoB;IAEpB,IAAIA,UAAUS,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,MAAMC,qSAAiBxB,uBAAAA,CAAqBc,QAAQ;IACpD,IAAIU,kBAAkBA,eAAeT,IAAI,KAAK,aAAa;QACzD,sBAAsB;QACtB,wEAAwE;QACxE,2DAA2D;QAC3D,0LAAOb,qBAAAA,EAAmBsB,eAAeC,YAAY,EAAE;IACzD;IACA,oFAAoF;IACpF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOH,QAAQC,OAAO,CAAC,CAAC;AAC1B;AAEA,SAASP,4BACPJ,SAAoB,EACpBY,cAA8B;IAE9B,IAAIZ,UAAUS,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,IAAIC,eAAeT,IAAI,KAAK,aAAa;QACvC,qDAAqD;QACrD,OAAOW,+BAA+Bd,UAAUe,KAAK,EAAEH;IACzD;IAEA,6DAA6D;IAC7D,2EAA2E;IAC3E,mCAAmC;IACnC,OAAOI,+BAA+BhB,WAAWY;AACnD;AAEA,SAASP,yBACPN,sBAAoC,EACpCC,SAAoB;IAEpB,IAAIA,UAAUS,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B,OAAO;QACL,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzB,CAACnB,UAAUoB,iBAAiB,EAC5B;YACA,OAAOC,wDACLtB,wBACAC;QAEJ,OAAO;YACL,OAAOsB,gCAAgCvB,wBAAwBC;QACjE;IACF;AACF;AAGA,MAAMuB,qBAAqB,IAAIC;AAE/B,MAAMC,gCAAgC,IAAID;AAK1C,SAASV,+BACPC,KAAa,EACbH,cAAoC;IAEpC,MAAMc,qBAAqBH,mBAAmBI,GAAG,CAACf;IAClD,IAAIc,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,6LAAUtC,qBAAAA,EACdsB,eAAeC,YAAY,EAC3B;IAGF,MAAMgB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,wMAAOnD,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMI,aACJ;mNACFlD,wBAAAA,EAAsBkD,YAAYxB;wBAClC,wMAAO/B,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBAAU;wBACb,MAAMG,aACJ;mNACFlD,wBAAAA,EAAsBkD,YAAYxB;wBAClC,wMAAO/B,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBAEA;oBAAS;wBACP,IAAI,OAAOD,SAAS,YAAY,mLAACtC,sBAAAA,CAAoB2C,GAAG,CAACL,OAAO;4BAC9D,MAAMI,mMAAa5C,+BAAAA,EACjB,gBACAwC;4BAEF,MAAMM,QAAQC,wBAAwBxB,OAAOqB;sNAC7CtD,+CAAAA,EACEiC,OACAqB,YACAE,OACA1B;wBAEJ;wBACA,wMAAO/B,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;QACAI,KAAIN,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,wFAAwF;YACxF,8FAA8F;YAC9F,kEAAkE;YAClE,IAAI,OAAOA,SAAS,UAAU;gBAC5B,MAAMI,mMAAa3C,oCAAAA,EACjB,gBACAuC;gBAEF,MAAMM,QAAQC,wBAAwBxB,OAAOqB;gBAC7CtD,yOAAAA,EACEiC,OACAqB,YACAE,OACA1B;YAEJ;YACA,wMAAO/B,iBAAAA,CAAewD,GAAG,CAACN,QAAQC;QACpC;QACAQ;YACE,MAAMJ,aACJ;YACF,MAAME,QAAQC,wBAAwBxB,OAAOqB;uMAC7CtD,8CAAAA,EACEiC,OACAqB,YACAE,OACA1B;QAEJ;IACF;IAEAW,mBAAmBkB,GAAG,CAAC7B,gBAAgBiB;IACvC,OAAOA;AACT;AAEA,SAASb,+BACPhB,SAAoB,EACpBY,cAAwD;IAExD,MAAMc,qBAAqBH,mBAAmBI,GAAG,CAAC3B;IAClD,IAAI0B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM3B,yBAAyB,CAAC;IAChC,mFAAmF;IACnF,qFAAqF;IACrF,+DAA+D;IAC/D,MAAM6B,UAAUlB,QAAQC,OAAO,CAACZ;IAEhC,MAAM8B,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,wMAAOnD,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMI,aACJ;wBACF,IAAIpC,UAAU0C,kBAAkB,EAAE;6BAChC/C,6NAAAA,EACEK,UAAUe,KAAK,EACfqB;wBAEJ,OAAO,IAAIxB,eAAeT,IAAI,KAAK,iBAAiB;4BAClD,+BAA+B;uNAC/BnB,uBAAAA,EACEgB,UAAUe,KAAK,EACfqB,YACAxB,eAAe+B,eAAe;wBAElC,OAAO;4BACL,mBAAmB;uNACnB5D,mCAAAA,EACEqD,YACApC,WACAY;wBAEJ;wBACA;oBACF;gBACA,KAAK;oBAAU;wBACb,MAAMwB,aACJ;wBACF,IAAIpC,UAAU0C,kBAAkB,EAAE;4BAChC/C,8NAAAA,EACEK,UAAUe,KAAK,EACfqB;wBAEJ,OAAO,IAAIxB,eAAeT,IAAI,KAAK,iBAAiB;4BAClD,+BAA+B;uNAC/BnB,uBAAAA,EACEgB,UAAUe,KAAK,EACfqB,YACAxB,eAAe+B,eAAe;wBAElC,OAAO;4BACL,mBAAmB;uNACnB5D,mCAAAA,EACEqD,YACApC,WACAY;wBAEJ;wBACA;oBACF;gBACA;oBAAS;wBACP,IAAI,OAAOoB,SAAS,YAAY,mLAACtC,sBAAAA,CAAoB2C,GAAG,CAACL,OAAO;4BAC9D,MAAMI,mMAAa5C,+BAAAA,EACjB,gBACAwC;4BAEF,IAAIhC,UAAU0C,kBAAkB,EAAE;qMAChC/C,yDAAAA,EACEK,UAAUe,KAAK,EACfqB;4BAEJ,OAAO,IAAIxB,eAAeT,IAAI,KAAK,iBAAiB;gCAClD,+BAA+B;2NAC/BnB,uBAAAA,EACEgB,UAAUe,KAAK,EACfqB,YACAxB,eAAe+B,eAAe;4BAElC,OAAO;gCACL,mBAAmB;2NACnB5D,mCAAAA,EACEqD,YACApC,WACAY;4BAEJ;wBACF;wBACA,wMAAO/B,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;QACAI,KAAIN,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,wFAAwF;YACxF,8FAA8F;YAC9F,kEAAkE;YAClE,IAAI,OAAOA,SAAS,UAAU;gBAC5B,MAAMI,mMAAa3C,oCAAAA,EACjB,gBACAuC;gBAEF,IAAIhC,UAAU0C,kBAAkB,EAAE;yLAChC/C,yDAAAA,EACEK,UAAUe,KAAK,EACfqB;gBAEJ,OAAO,IAAIxB,eAAeT,IAAI,KAAK,iBAAiB;oBAClD,+BAA+B;qBAC/BnB,iNAAAA,EACEgB,UAAUe,KAAK,EACfqB,YACAxB,eAAe+B,eAAe;gBAElC,OAAO;oBACL,mBAAmB;+MACnB5D,mCAAAA,EACEqD,YACApC,WACAY;gBAEJ;gBACA,OAAO;YACT;YACA,wMAAO/B,iBAAAA,CAAewD,GAAG,CAACN,QAAQC;QACpC;QACAQ;YACE,MAAMJ,aACJ;YACF,IAAIpC,UAAU0C,kBAAkB,EAAE;oBAChC/C,0NAAAA,EACEK,UAAUe,KAAK,EACfqB;YAEJ,OAAO,IAAIxB,eAAeT,IAAI,KAAK,iBAAiB;gBAClD,+BAA+B;2MAC/BnB,uBAAAA,EACEgB,UAAUe,KAAK,EACfqB,YACAxB,eAAe+B,eAAe;YAElC,OAAO;gBACL,mBAAmB;2MACnB5D,mCAAAA,EAAiCqD,YAAYpC,WAAWY;YAC1D;QACF;IACF;IAEAW,mBAAmBkB,GAAG,CAACzC,WAAW6B;IAClC,OAAOA;AACT;AAOO,SAASe,0CACd5C,SAAoB;IAEpB,MAAM0B,qBAAqBD,8BAA8BE,GAAG,CAAC3B;IAC7D,IAAI0B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUlB,QAAQC,OAAO,CAAC,CAAC;IAEjC,MAAMkB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,wEAAwE;gBACxE,mEAAmE;gBACnE,+DAA+D;gBAC/D,oBAAoB;gBACpB,wMAAOnD,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IACE,OAAOD,SAAS,YACfA,CAAAA,SAAS,UAAU,kLAACtC,uBAAAA,CAAoB2C,GAAG,CAACL,KAAI,GACjD;sLACApC,uCAAAA,EAAqCI;YACvC;YAEA,wMAAOnB,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAI,KAAIN,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,uFAAuF;YACvF,8FAA8F;YAC9F,kEAAkE;YAClE,IACE,OAAOA,SAAS,YACfA,CAAAA,SAAS,UAAU,mLAACtC,sBAAAA,CAAoB2C,GAAG,CAACL,KAAI,GACjD;sLACApC,uCAAAA,EAAqCI;YACvC;YAEA,wMAAOnB,iBAAAA,CAAewD,GAAG,CAACN,QAAQC;QACpC;QACAQ;aACE5C,4MAAAA,EAAqCI;QACvC;IACF;IAEAyB,8BAA8BgB,GAAG,CAACzC,WAAW6B;IAC7C,OAAOA;AACT;AAEA,SAASP,gCACPvB,sBAAoC,EACpC8C,KAAgB;IAEhB,MAAMnB,qBAAqBH,mBAAmBI,GAAG,CAAC5B;IAClD,IAAI2B,oBAAoB;QACtB,OAAOA;IACT;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUlB,QAAQC,OAAO,CAACZ;IAChCwB,mBAAmBkB,GAAG,CAAC1C,wBAAwB6B;IAE/CM,OAAOY,IAAI,CAAC/C,wBAAwBgD,OAAO,CAAC,CAACf;QAC3C,IAAI,mLAACtC,sBAAAA,CAAoB2C,GAAG,CAACL,OAAO;YAClCE,OAAOc,cAAc,CAACpB,SAASI,MAAM;gBACnCL;oBACE,MAAM1B,oSAAgBb,uBAAAA,CAAqBc,QAAQ;+MACnDjB,kCAAAA,EAAgC4D,OAAO5C;oBACvC,OAAOF,sBAAsB,CAACiC,KAAK;gBACrC;gBACAS,KAAIQ,KAAK;oBACPf,OAAOc,cAAc,CAACpB,SAASI,MAAM;wBACnCiB;wBACAC,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOxB;AACT;AAEA,SAASP,wDACPtB,sBAAoC,EACpC8C,KAAgB;IAEhB,MAAMnB,qBAAqBH,mBAAmBI,GAAG,CAAC5B;IAClD,IAAI2B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM2B,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,0HAA0H;IAC1H,uIAAuI;IACvI,wIAAwI;IACxI,8IAA8I;IAC9I,6IAA6I;IAC7I,+GAA+G;IAC/G,IAAIC,qBAAqB;IACzB,MAAMC,oBAAoB,IAAI3B,MAAM/B,wBAAwB;QAC1D4B,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,YAAYwB,oBAAoB;gBAClD,IAAIX,MAAMH,kBAAkB,EAAE;oBAC5B,MAAMN,mMAAa5C,+BAAAA,EAA6B,gBAAgBwC;qBAChErC,6NAAAA,EACEkD,MAAM9B,KAAK,EACXqB;gBAEJ;gBACA,MAAMnC,oSAAgBb,uBAAAA,CAAqBc,QAAQ;2MACnDjB,kCAAAA,EAAgC4D,OAAO5C;YACzC;YACA,wMAAOpB,iBAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAI,KAAIN,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IAAIa,MAAMH,kBAAkB,EAAE;oBAC5B,MAAMN,mMAAa3C,oCAAAA,EACjB,gBACAuC;qBAEFrC,6NAAAA,EACEkD,MAAM9B,KAAK,EACXqB;gBAEJ;YACF;YACA,OAAOsB,QAAQrB,GAAG,CAACN,QAAQC;QAC7B;QACAQ,SAAQT,MAAM;YACZ,IAAIc,MAAMH,kBAAkB,EAAE;gBAC5B,MAAMN,aACJ;sLACFzC,wDAAAA,EACEkD,MAAM9B,KAAK,EACXqB;YAEJ;YACA,OAAOsB,QAAQlB,OAAO,CAACT;QACzB;IACF;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAMH,UAAU,IAAIlB,QAAsB,CAACC,sKACzCd,oBAAAA,EAAkB,IAAMc,QAAQZ;IAElC6B,QAAQ+B,IAAI,CAAC;QACXH,qBAAqB;IACvB;IAEAtB,OAAOY,IAAI,CAAC/C,wBAAwBgD,OAAO,CAAC,CAACf;QAC3C,sLAAItC,sBAAAA,CAAoB2C,GAAG,CAACL,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEuB,oBAAoBK,IAAI,CAAC5B;QAC3B,OAAO;YACLqB,kBAAkBQ,GAAG,CAAC7B;YACtBE,OAAOc,cAAc,CAACpB,SAASI,MAAM;gBACnCL;oBACE,OAAO8B,iBAAiB,CAACzB,KAAK;gBAChC;gBACAS,KAAIqB,QAAQ;oBACV5B,OAAOc,cAAc,CAACpB,SAASI,MAAM;wBACnCiB,OAAOa;wBACPZ,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,MAAMvB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAID,SAAS,UAAUa,MAAMH,kBAAkB,EAAE;gBAC/C,MAAMN,aAAa;sLACnBzC,wDAAAA,EACEkD,MAAM9B,KAAK,EACXqB;YAEJ;YACA,IAAI,OAAOJ,SAAS,UAAU;gBAC5B,IACE,mLAACtC,sBAAAA,CAAoB2C,GAAG,CAACL,SACxBqB,CAAAA,kBAAkBhB,GAAG,CAACL,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/B0B,QAAQrB,GAAG,CAACN,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,mMAAa5C,+BAAAA,EAA6B,gBAAgBwC;oBAChE+B,UAAUlB,MAAM9B,KAAK,EAAEqB;gBACzB;YACF;YACA,OAAOvD,kNAAAA,CAAe8C,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAQ,KAAIV,MAAM,EAAEC,IAAI,EAAEiB,KAAK,EAAEhB,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BqB,kBAAkBW,MAAM,CAAChC;YAC3B;YACA,OAAO0B,QAAQjB,GAAG,CAACV,QAAQC,MAAMiB,OAAOhB;QAC1C;QACAI,KAAIN,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,mLAACtC,sBAAAA,CAAoB2C,GAAG,CAACL,SACxBqB,CAAAA,kBAAkBhB,GAAG,CAACL,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/B0B,QAAQrB,GAAG,CAACN,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,mMAAa3C,oCAAAA,EACjB,gBACAuC;oBAEF+B,UAAUlB,MAAM9B,KAAK,EAAEqB;gBACzB;YACF;YACA,OAAOsB,QAAQrB,GAAG,CAACN,QAAQC;QAC7B;QACAQ,SAAQT,MAAM;YACZ,MAAMK,aAAa;YACnB2B,UAAUlB,MAAM9B,KAAK,EAAEqB,YAAYmB;YACnC,OAAOG,QAAQlB,OAAO,CAACT;QACzB;IACF;IAEAR,mBAAmBkB,GAAG,CAAC1C,wBAAwB8B;IAC/C,OAAOA;AACT;AAEA,SAASkC,UACPhD,KAAyB,EACzBqB,UAAkB,EAClB6B,iBAAiC;IAEjC,gCAAgC;IAChC,IAAIA,qBAAqBA,kBAAkBC,MAAM,GAAG,GAAG;QACrDC,6BAA6BpD,OAAOqB,YAAY6B;IAClD,OAAO;QACLG,kBAAkBrD,OAAOqB;IAC3B;IAEA,MAAMnC,oSAAgBb,uBAAAA,CAAqBc,QAAQ;IACnD,IACED,iBACAA,cAAcE,IAAI,KAAK,aACvBF,cAAcoE,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAerE;mMACrBd,yCAAAA,EAAuCmF;IACzC;AACF;AAEA,MAAMF,wBAAoB7E,gQAAAA,EACxBgD;AAGF,MAAM4B,qPACJ5E,8CAAAA,EAA4CgF;AAE9C,SAAShC,wBACPxB,KAAyB,EACzBqB,UAAkB;IAElB,MAAMoC,SAASzD,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAI0D,MACT,GAAGD,OAAO,KAAK,EAAEpC,WAAW,EAAE,CAAC,GAC7B,CAAC,gEAAgE,CAAC,GAClE,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASmC,iCACPxD,KAAyB,EACzBqB,UAAkB,EAClB6B,iBAAgC;IAEhC,MAAMO,SAASzD,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAI0D,MACT,GAAGD,OAAO,KAAK,EAAEpC,WAAW,EAAE,CAAC,GAC7B,CAAC,gEAAgE,CAAC,GAClE,CAAC,gEAAgE,CAAC,GAClE,CAAC,iEAAiE,CAAC,GACnE,GAAGsC,4BAA4BT,mBAAmB,EAAE,CAAC,GACrD,CAAC,8DAA8D,CAAC,GAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASS,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWT,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,+KAAI7E,iBAAAA,CACR,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,CAAC,EAAE,EAAEsF,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAO,CAAC,EAAE,EAAEA,UAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIC,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,WAAWT,MAAM,GAAG,GAAGW,IAAK;oBAC9CD,eAAe,CAAC,EAAE,EAAED,UAAU,CAACE,EAAE,CAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,CAAC,QAAQ,EAAED,UAAU,CAACA,WAAWT,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAOU;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4148, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/server/request/params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    const fallbackParams = workStore.fallbackRouteParams\n    if (fallbackParams) {\n      for (let key in underlyingParams) {\n        if (fallbackParams.has(key)) {\n          // This params object has one of more fallback params so we need to consider\n          // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n          // we encode this as a promise that never resolves\n          return makeHangingPromise(prerenderStore.renderSignal, '`params`')\n        }\n      }\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      if (prerenderStore.type === 'prerender') {\n        // We are in a dynamicIO (PPR or otherwise) prerender\n        return makeAbortingExoticParams(\n          underlyingParams,\n          workStore.route,\n          prerenderStore\n        )\n      }\n      // remaining cases are prerender-ppr and prerender-legacy\n      // We aren't in a dynamicIO prerender but we do have fallback params at this\n      // level so we need to make an erroring exotic params object which will postpone\n      // if you access the fallback params\n      return makeErroringExoticParams(\n        underlyingParams,\n        fallbackParams,\n        workStore,\n        prerenderStore\n      )\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  if (process.env.NODE_ENV === 'development' && !workStore.isPrefetchRequest) {\n    return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n      underlyingParams,\n      workStore\n    )\n  } else {\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeAbortingExoticParams(\n  underlyingParams: Params,\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = makeHangingPromise<Params>(\n    prerenderStore.renderSignal,\n    '`params`'\n  )\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const expression = describeStringPropertyAccess('params', prop)\n          const error = createParamsAccessError(route, expression)\n          abortAndThrowOnSynchronousRequestDataAccess(\n            route,\n            expression,\n            error,\n            prerenderStore\n          )\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["ReflectAdapter", "abortAndThrowOnSynchronousRequestDataAccess", "throwToInterruptStaticGeneration", "postponeWithTracking", "trackSynchronousRequestDataAccessInDev", "workUnitAsyncStorage", "InvariantError", "describeStringPropertyAccess", "wellKnownProperties", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "scheduleImmediate", "createParamsFromClient", "underlyingParams", "workStore", "workUnitStore", "getStore", "type", "createPrerenderParams", "createRenderParams", "createServerParamsForMetadata", "createServerParamsForServerSegment", "createServerParamsForRoute", "createPrerenderParamsForClientSegment", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "renderSignal", "Promise", "resolve", "hasSomeFallbackParams", "makeAbortingExoticParams", "route", "makeErroringExoticParams", "makeUntrackedExoticParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "cachedParams", "get", "promise", "set", "Object", "keys", "for<PERSON>ach", "prop", "defineProperty", "expression", "error", "createParamsAccessError", "newValue", "value", "writable", "enumerable", "configurable", "augmentedUnderlying", "dynamicTracking", "store", "proxiedProperties", "Set", "unproxiedProperties", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "syncIODev", "delete", "ownKeys", "Reflect", "missingProperties", "prerenderPhase", "requestStore", "length", "warnForIncompleteEnumeration", "warnForSyncAccess", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "description", "i"], "mappings": ";;;;;;;AAGA,SAASA,cAAc,QAAQ,yCAAwC;AACvE,SACEC,2CAA2C,EAC3CC,gCAAgC,EAChCC,oBAAoB,EACpBC,sCAAsC,QACjC,kCAAiC;AAExC,SACEC,oBAAoB,QAKf,iDAAgD;AACvD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,4BAA4B,EAC5BC,mBAAmB,QACd,uCAAsC;AAC7C,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,iBAAiB,QAAQ,sBAAqB;;;;;;;;;AAiChD,SAASC,uBACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,oSAAgBV,uBAAAA,CAAqBW,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBL,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOI,mBAAmBN,kBAAkBC;AAC9C;AAIO,MAAMM,gCAAgCC,mCAAkC;AAGxE,SAASC,2BACdT,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,oSAAgBV,uBAAAA,CAAqBW,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBL,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOI,mBAAmBN,kBAAkBC;AAC9C;AAEO,SAASO,mCACdR,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBV,2SAAAA,CAAqBW,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBL,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOI,mBAAmBN,kBAAkBC;AAC9C;AAEO,SAASS,sCACdV,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMU,qSAAiBnB,uBAAAA,CAAqBW,QAAQ;IACpD,IAAIQ,kBAAkBA,eAAeP,IAAI,KAAK,aAAa;QACzD,MAAMQ,iBAAiBX,UAAUY,mBAAmB;QACpD,IAAID,gBAAgB;YAClB,IAAK,IAAIE,OAAOd,iBAAkB;gBAChC,IAAIY,eAAeG,GAAG,CAACD,MAAM;oBAC3B,4EAA4E;oBAC5E,+EAA+E;oBAC/E,kDAAkD;oBAClD,0LAAOlB,qBAAAA,EAAmBe,eAAeK,YAAY,EAAE;gBACzD;YACF;QACF;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOC,QAAQC,OAAO,CAAClB;AACzB;AAEA,SAASK,sBACPL,gBAAwB,EACxBC,SAAoB,EACpBU,cAA8B;IAE9B,MAAMC,iBAAiBX,UAAUY,mBAAmB;IACpD,IAAID,gBAAgB;QAClB,IAAIO,wBAAwB;QAC5B,IAAK,MAAML,OAAOd,iBAAkB;YAClC,IAAIY,eAAeG,GAAG,CAACD,MAAM;gBAC3BK,wBAAwB;gBACxB;YACF;QACF;QAEA,IAAIA,uBAAuB;YACzB,mFAAmF;YACnF,IAAIR,eAAeP,IAAI,KAAK,aAAa;gBACvC,qDAAqD;gBACrD,OAAOgB,yBACLpB,kBACAC,UAAUoB,KAAK,EACfV;YAEJ;YACA,yDAAyD;YACzD,4EAA4E;YAC5E,gFAAgF;YAChF,oCAAoC;YACpC,OAAOW,yBACLtB,kBACAY,gBACAX,WACAU;QAEJ;IACF;IAEA,qFAAqF;IACrF,OAAOY,0BAA0BvB;AACnC;AAEA,SAASM,mBACPN,gBAAwB,EACxBC,SAAoB;IAEpB,IAAIuB,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiB,CAACzB,UAAU0B,iBAAiB,EAAE;QAC1E,OAAOC,kDACL5B,kBACAC;IAEJ,OAAO;QACL,OAAOsB,0BAA0BvB;IACnC;AACF;AAGA,MAAM6B,eAAe,IAAIC;AAEzB,SAASV,yBACPpB,gBAAwB,EACxBqB,KAAa,EACbV,cAAoC;IAEpC,MAAMoB,eAAeF,aAAaG,GAAG,CAAChC;IACtC,IAAI+B,cAAc;QAChB,OAAOA;IACT;IAEA,MAAME,6LAAUrC,qBAAAA,EACde,eAAeK,YAAY,EAC3B;IAEFa,aAAaK,GAAG,CAAClC,kBAAkBiC;IAEnCE,OAAOC,IAAI,CAACpC,kBAAkBqC,OAAO,CAAC,CAACC;QACrC,sLAAI3C,sBAAAA,CAAoBoB,GAAG,CAACuB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLH,OAAOI,cAAc,CAACN,SAASK,MAAM;gBACnCN;oBACE,MAAMQ,mMAAa9C,+BAAAA,EAA6B,UAAU4C;oBAC1D,MAAMG,QAAQC,wBAAwBrB,OAAOmB;+MAC7CpD,8CAAAA,EACEiC,OACAmB,YACAC,OACA9B;gBAEJ;gBACAuB,KAAIS,QAAQ;oBACVR,OAAOI,cAAc,CAACN,SAASK,MAAM;wBACnCM,OAAOD;wBACPE,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOd;AACT;AAEA,SAASX,yBACPtB,gBAAwB,EACxBY,cAAmC,EACnCX,SAAoB,EACpBU,cAAwD;IAExD,MAAMoB,eAAeF,aAAaG,GAAG,CAAChC;IACtC,IAAI+B,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMiB,sBAAsB;QAAE,GAAGhD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMiC,UAAUhB,QAAQC,OAAO,CAAC8B;IAChCnB,aAAaK,GAAG,CAAClC,kBAAkBiC;IAEnCE,OAAOC,IAAI,CAACpC,kBAAkBqC,OAAO,CAAC,CAACC;QACrC,sLAAI3C,sBAAAA,CAAoBoB,GAAG,CAACuB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAI1B,eAAeG,GAAG,CAACuB,OAAO;gBAC5BH,OAAOI,cAAc,CAACS,qBAAqBV,MAAM;oBAC/CN;wBACE,MAAMQ,mMAAa9C,+BAAAA,EAA6B,UAAU4C;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI3B,eAAeP,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;uNAC/Bd,uBAAAA,EACEW,UAAUoB,KAAK,EACfmB,YACA7B,eAAesC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;uNACnB5D,mCAAAA,EACEmD,YACAvC,WACAU;wBAEJ;oBACF;oBACAmC,YAAY;gBACd;gBACAX,OAAOI,cAAc,CAACN,SAASK,MAAM;oBACnCN;wBACE,MAAMQ,mMAAa9C,+BAAAA,EAA6B,UAAU4C;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI3B,eAAeP,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;6BAC/Bd,iNAAAA,EACEW,UAAUoB,KAAK,EACfmB,YACA7B,eAAesC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;gCACnB5D,0NAAAA,EACEmD,YACAvC,WACAU;wBAEJ;oBACF;oBACAuB,KAAIS,QAAQ;wBACVR,OAAOI,cAAc,CAACN,SAASK,MAAM;4BACnCM,OAAOD;4BACPE,UAAU;4BACVC,YAAY;wBACd;oBACF;oBACAA,YAAY;oBACZC,cAAc;gBAChB;YACF,OAAO;;gBACHd,OAAe,CAACK,KAAK,GAAGtC,gBAAgB,CAACsC,KAAK;YAClD;QACF;IACF;IAEA,OAAOL;AACT;AAEA,SAASV,0BAA0BvB,gBAAwB;IACzD,MAAM+B,eAAeF,aAAaG,GAAG,CAAChC;IACtC,IAAI+B,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUhB,QAAQC,OAAO,CAAClB;IAChC6B,aAAaK,GAAG,CAAClC,kBAAkBiC;IAEnCE,OAAOC,IAAI,CAACpC,kBAAkBqC,OAAO,CAAC,CAACC;QACrC,sLAAI3C,sBAAAA,CAAoBoB,GAAG,CAACuB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHL,OAAe,CAACK,KAAK,GAAGtC,gBAAgB,CAACsC,KAAK;QAClD;IACF;IAEA,OAAOL;AACT;AAEA,SAASL,kDACP5B,gBAAwB,EACxBkD,KAAgB;IAEhB,MAAMnB,eAAeF,aAAaG,GAAG,CAAChC;IACtC,IAAI+B,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAU,IAAIhB,QAAgB,CAACC,qKACnCpB,qBAAAA,EAAkB,IAAMoB,QAAQlB;IAGlC,MAAMmD,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7ClB,OAAOC,IAAI,CAACpC,kBAAkBqC,OAAO,CAAC,CAACC;QACrC,sLAAI3C,sBAAAA,CAAoBoB,GAAG,CAACuB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEe,oBAAoBC,IAAI,CAAChB;QAC3B,OAAO;YACLa,kBAAkBI,GAAG,CAACjB;YACpBL,OAAe,CAACK,KAAK,GAAGtC,gBAAgB,CAACsC,KAAK;QAClD;IACF;IAEA,MAAMkB,iBAAiB,IAAIC,MAAMxB,SAAS;QACxCD,KAAI0B,MAAM,EAAEpB,IAAI,EAAEqB,QAAQ;YACxB,IAAI,OAAOrB,SAAS,UAAU;gBAC5B,IACE,AACAa,kBAAkBpC,GAAG,CAACuB,OACtB,0CAFuE;oBAGvE,MAAME,mMAAa9C,+BAAAA,EAA6B,UAAU4C;oBAC1DsB,UAAUV,MAAM7B,KAAK,EAAEmB;gBACzB;YACF;YACA,wMAAOrD,iBAAAA,CAAe6C,GAAG,CAAC0B,QAAQpB,MAAMqB;QAC1C;QACAzB,KAAIwB,MAAM,EAAEpB,IAAI,EAAEM,KAAK,EAAEe,QAAQ;YAC/B,IAAI,OAAOrB,SAAS,UAAU;gBAC5Ba,kBAAkBU,MAAM,CAACvB;YAC3B;YACA,wMAAOnD,iBAAAA,CAAe+C,GAAG,CAACwB,QAAQpB,MAAMM,OAAOe;QACjD;QACAG,SAAQJ,MAAM;YACZ,MAAMlB,aAAa;YACnBoB,UAAUV,MAAM7B,KAAK,EAAEmB,YAAYa;YACnC,OAAOU,QAAQD,OAAO,CAACJ;QACzB;IACF;IAEA7B,aAAaK,GAAG,CAAClC,kBAAkBwD;IACnC,OAAOA;AACT;AAEA,SAASI,UACPvC,KAAyB,EACzBmB,UAAkB,EAClBwB,iBAAiC;IAEjC,MAAM9D,oSAAgBV,uBAAAA,CAAqBW,QAAQ;IACnD,IACED,iBACAA,cAAcE,IAAI,KAAK,aACvBF,cAAc+D,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAehE;SACrBX,mOAAAA,EAAuC2E;IACzC;IACA,gCAAgC;IAChC,IAAIF,qBAAqBA,kBAAkBG,MAAM,GAAG,GAAG;QACrDC,6BAA6B/C,OAAOmB,YAAYwB;IAClD,OAAO;QACLK,kBAAkBhD,OAAOmB;IAC3B;AACF;AAEA,MAAM6B,qBAAoBxE,mQAAAA,EACxB6C;AAGF,MAAM0B,qPACJvE,8CAAAA,EAA4CyE;AAE9C,SAAS5B,wBACPrB,KAAyB,EACzBmB,UAAkB;IAElB,MAAM+B,SAASlD,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAImD,MACT,GAAGD,OAAO,KAAK,EAAE/B,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAAS8B,iCACPjD,KAAyB,EACzBmB,UAAkB,EAClBwB,iBAAgC;IAEhC,MAAMO,SAASlD,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAImD,MACT,GAAGD,OAAO,KAAK,EAAE/B,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,gEAAgE,CAAC,GAClE,CAAC,mDAAmD,CAAC,GACrD,GAAGiC,4BAA4BT,mBAAmB,EAAE,CAAC,GACrD,CAAC,8DAA8D,CAAC,GAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASS,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWP,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,+KAAI1E,iBAAAA,CACR,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,CAAC,EAAE,EAAEiF,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAO,CAAC,EAAE,EAAEA,UAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIC,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,WAAWP,MAAM,GAAG,GAAGS,IAAK;oBAC9CD,eAAe,CAAC,EAAE,EAAED,UAAU,CAACE,EAAE,CAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,CAAC,QAAQ,EAAED,UAAU,CAACA,WAAWP,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAOQ;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4499, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["InvariantError", "ClientPageRoot", "Component", "searchParams", "params", "promises", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;;AAGA,SAASA,cAAc,QAAQ,mCAAkC;AAHjE;;;AAeO,SAASC,eAAe,KAW9B;IAX8B,IAAA,EAC7BC,SAAS,EACTC,YAAY,EACZC,MAAM,EACN,AACAC,QAAQ,EAMT,GAX8B,gDAIgC;IAQ7D,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQJ,iBAAiBK,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,+KAAIX,iBAAAA,CACR,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEa,4BAA4B,EAAE,GACpCL,QAAQ;QACVC,qBAAqBI,6BAA6BV,cAAcQ;QAEhE,MAAM,EAAEG,sBAAsB,EAAE,GAC9BN,QAAQ;QACVE,eAAeI,uBAAuBV,QAAQO;QAE9C,OAAA,WAAA,+NAAO,MAAA,EAACT,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD,OAAO;QACL,MAAM,EAAEM,kCAAkC,EAAE,GAC1CP,QAAQ;QACV,MAAMC,qBAAqBM,mCAAmCZ;QAC9D,MAAM,EAAEa,4BAA4B,EAAE,GACpCR,QAAQ;QACV,MAAME,eAAeM,6BAA6BZ;QAElD,OAAA,WAAA,+NAAO,MAAA,EAACF,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4548, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when dynamicIO is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["InvariantError", "ClientSegmentRoot", "Component", "slots", "params", "promise", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;;AAEA,SAASA,cAAc,QAAQ,mCAAkC;AAFjE;;;AAcO,SAASC,kBAAkB,KAWjC;IAXiC,IAAA,EAChCC,SAAS,EACTC,KAAK,EACLC,MAAM,EACN,AACAC,OAAO,EAMR,GAXiC,iDAI6B;IAQ7D,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQH,iBAAiBI,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,+KAAIV,iBAAAA,CACR,uGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEY,sBAAsB,EAAE,GAC9BJ,QAAQ;QACVC,eAAeG,uBAAuBR,QAAQM;QAE9C,OAAA,WAAA,+NAAO,MAAA,EAACR,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC,OAAO;QACL,MAAM,EAAEI,4BAA4B,EAAE,GACpCL,QAAQ;QACV,MAAMC,eAAeI,6BAA6BT;QAClD,OAAA,WAAA,+NAAO,MAAA,EAACF,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4592, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/metadata/browser-resolved-metadata.tsx"], "sourcesContent": ["import { use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport function BrowserResolvedMetadata({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { metadata, error } = use(promise)\n  // If there's metadata error on client, discard the browser metadata\n  // and let metadata outlet deal with the error. This will avoid the duplication metadata.\n  if (error) return null\n  return metadata\n}\n"], "names": ["use", "BrowserResolvedMetadata", "promise", "metadata", "error"], "mappings": ";;;AAAA,SAASA,GAAG,QAAQ,QAAO;;AAGpB,SAASC,wBAAwB,KAIvC;IAJuC,IAAA,EACtCC,OAAO,EAGR,GAJuC;IAKtC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAE,6MAAGJ,MAAAA,EAAIE;IAChC,oEAAoE;IACpE,yFAAyF;IACzF,IAAIE,OAAO,OAAO;IAClB,OAAOD;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4610, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-metadata.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ServerInsertedMetadata\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedMetadata"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4618, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/metadata/server-inserted-metadata.tsx"], "sourcesContent": ["import { use, useContext } from 'react'\nimport {\n  type MetadataResolver,\n  ServerInsertedMetadataContext,\n} from '../../../shared/lib/server-inserted-metadata.shared-runtime'\nimport type { StreamingMetadataResolvedState } from './types'\n\n// Receives a metadata resolver setter from the context, and will pass the metadata resolving promise to\n// the context where we gonna use it to resolve the metadata, and render as string to append in <body>.\nconst useServerInsertedMetadata = (metadataResolver: MetadataResolver) => {\n  const setMetadataResolver = useContext(ServerInsertedMetadataContext)\n\n  if (setMetadataResolver) {\n    setMetadataResolver(metadataResolver)\n  }\n}\n\nexport function ServerInsertMetadata({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  // Apply use() to the metadata promise to suspend the rendering in SSR.\n  const { metadata } = use(promise)\n  // Insert metadata into the HTML stream through the `useServerInsertedMetadata`\n  useServerInsertedMetadata(() => metadata)\n\n  return null\n}\n"], "names": ["use", "useContext", "ServerInsertedMetadataContext", "useServerInsertedMetadata", "metadataResolver", "setMetadataResolver", "ServerInsertMetadata", "promise", "metadata"], "mappings": ";;;AAAA,SAASA,GAAG,EAAEC,UAAU,QAAQ,QAAO;AACvC,SAEEC,6BAA6B,QACxB,8DAA6D;;;AAGpE,wGAAwG;AACxG,uGAAuG;AACvG,MAAMC,4BAA4B,CAACC;IACjC,MAAMC,gOAAsBJ,aAAAA,EAAWC,oQAAAA;IAEvC,IAAIG,qBAAqB;QACvBA,oBAAoBD;IACtB;AACF;AAEO,SAASE,qBAAqB,KAIpC;IAJoC,IAAA,EACnCC,OAAO,EAGR,GAJoC;IAKnC,uEAAuE;IACvE,MAAM,EAAEC,QAAQ,EAAE,6MAAGR,MAAAA,EAAIO;IACzB,+EAA+E;IAC/EJ,0BAA0B,IAAMK;IAEhC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4647, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/metadata/async-metadata.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport const AsyncMetadata =\n  typeof window === 'undefined'\n    ? (\n        require('./server-inserted-metadata') as typeof import('./server-inserted-metadata')\n      ).ServerInsertMetadata\n    : (\n        require('./browser-resolved-metadata') as typeof import('./browser-resolved-metadata')\n      ).BrowserResolvedMetadata\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n"], "names": ["Suspense", "use", "AsyncMetadata", "window", "require", "ServerInsertMetadata", "BrowserResolvedMetadata", "MetadataOutlet", "promise", "error", "digest", "AsyncMetadataOutlet", "fallback"], "mappings": ";;;;;AAEA,SAASA,QAAQ,EAAEC,GAAG,QAAQ,QAAO;AAFrC;;;AAKO,MAAMC,gBACX,OAAOC,WAAW,cAEZC,QAAQ,sIACRC,oBAAoB,GAEpBD,QAAQ,uIACRE,uBAAuB,CAAA;AAE/B,SAASC,eAAe,KAIvB;IAJuB,IAAA,EACtBC,OAAO,EAGR,GAJuB;IAKtB,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE,6MAAGT,MAAAA,EAAIO;IAC9B,IAAIC,OAAO;QACT,IAAIC,QAAQ;YACV,2FAA2F;YAC3F,mGAAmG;;YACjGD,MAAcC,MAAM,GAAGA;QAC3B;QACA,MAAMD;IACR;IACA,OAAO;AACT;AAEO,SAASE,oBAAoB,KAInC;IAJmC,IAAA,EAClCH,OAAO,EAGR,GAJmC;IAKlC,OAAA,WAAA,+NACE,MAAA,wMAACR,WAAAA,EAAAA;QAASY,UAAU;kBAClB,WAAA,+NAAA,MAAA,EAACL,gBAAAA;YAAeC,SAASA;;;AAG/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4686, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/dist/src/client/components/metadata/metadata-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../../lib/metadata/metadata-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "NameSpace", "children", "MetadataBoundary", "slice", "ViewportBoundary", "OutletBoundary"], "mappings": ";;;;;AAEA,SACEA,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,QACf,2CAA0C;AANjD;;AAQA,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,iLAACH,yBAAAA,CAAuB,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCI,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACH,yMAAAA,CAAuB,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCG,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,iLAACF,uBAAAA,CAAqB,EAAE,SAAU,KAIjC;QAJiC,IAAA,EAChCE,QAAQ,EAGT,GAJiC;QAKhC,OAAOA;IACT;AACF;AAEO,MAAMC,mBACX,AACA,4DAA4D,oBADoB;AAEhFF,SAAS,iLAACH,yBAAAA,CAAuBM,KAAK,CAAC,GAAoC,CAAA;AAEtE,MAAMC,mBACX,AACA,4DAA4D,oBADoB;AAEhFJ,SAAS,iLAACF,yBAAAA,CAAuBK,KAAK,CAAC,GAAoC,CAAA;AAEtE,MAAME,iBACX,AACA,4DAA4D,oBADoB;AAEhFL,SAAS,iLAACD,uBAAAA,CAAqBI,KAAK,CAAC,GAAkC,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4721, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4733, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/client/components/navigation-untracked.ts"], "sourcesContent": ["import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams() {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) return false\n\n    const { fallbackRouteParams } = workStore\n    if (!fallbackRouteParams || fallbackRouteParams.size === 0) return false\n\n    return true\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n"], "names": ["useUntrackedPathname", "hasFallbackRouteParams", "window", "workAsyncStorage", "require", "workStore", "getStore", "fallbackRouteParams", "size", "useContext", "PathnameContext"], "mappings": ";;;;+BAsCgBA,wBAAAA;;;eAAAA;;;uBAtCW;iDACK;AAEhC;;;;;CAKC,GACD,SAASC;IACP,IAAI,OAAOC,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,MAAMC,YAAYF,iBAAiBG,QAAQ;QAC3C,IAAI,CAACD,WAAW,OAAO;QAEvB,MAAM,EAAEE,mBAAmB,EAAE,GAAGF;QAChC,IAAI,CAACE,uBAAuBA,oBAAoBC,IAAI,KAAK,GAAG,OAAO;QAEnE,OAAO;IACT;IAEA,OAAO;AACT;AAaO,SAASR;IACd,qEAAqE;IACrE,8EAA8E;IAC9E,8EAA8E;IAC9E,uBAAuB;IACvB,IAAIC,0BAA0B;QAC5B,OAAO;IACT;IAEA,2EAA2E;IAC3E,qDAAqD;IACrD,sDAAsD;IACtD,OAAOQ,CAAAA,GAAAA,OAAAA,UAAU,EAACC,iCAAAA,eAAe;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4787, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4863, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4891, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4947, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4974, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/client/components/router-reducer/create-href-from-url.ts"], "sourcesContent": ["export function createHrefFromUrl(\n  url: Pick<URL, 'pathname' | 'search' | 'hash'>,\n  includeHash: boolean = true\n): string {\n  return url.pathname + url.search + (includeHash ? url.hash : '')\n}\n"], "names": ["createHrefFromUrl", "url", "includeHash", "pathname", "search", "hash"], "mappings": ";;;;+BAAgBA,qBAAAA;;;eAAAA;;;AAAT,SAASA,kBACdC,GAA8C,EAC9CC,WAA2B;IAA3BA,IAAAA,gBAAAA,KAAAA,GAAAA,cAAuB;IAEvB,OAAOD,IAAIE,QAAQ,GAAGF,IAAIG,MAAM,GAAIF,CAAAA,cAAcD,IAAII,IAAI,GAAG,EAAC;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5000, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/client/components/nav-failure-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n"], "names": ["handleHardNavError", "useNavFailureHandler", "error", "window", "next", "__pendingUrl", "createHrefFromUrl", "URL", "location", "href", "console", "toString", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "useEffect", "uncaughtExceptionHandler", "evt", "reason", "addEventListener", "removeEventListener"], "mappings": ";;;;;;;;;;;;;;;IAGgBA,kBAAkB,EAAA;eAAlBA;;IAkBAC,oBAAoB,EAAA;eAApBA;;;uBArBU;mCACQ;AAE3B,SAASD,mBAAmBE,KAAc;IAC/C,IACEA,SACA,OAAOC,WAAW,eAClBA,OAAOC,IAAI,CAACC,YAAY,IACxBC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC,IAAIC,IAAIJ,OAAOK,QAAQ,CAACC,IAAI,OAC5CH,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACH,OAAOC,IAAI,CAACC,YAAY,GAC5C;QACAK,QAAQR,KAAK,CACV,qEACDA;QAEFC,OAAOK,QAAQ,CAACC,IAAI,GAAGN,OAAOC,IAAI,CAACC,YAAY,CAACM,QAAQ;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAASV;IACd,IAAIW,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;IAuB5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5049, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/formsaas/autofillx/node_modules/next/src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\n\nconst workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    if (this.state.error) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nexport function GlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default GlobalError\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GlobalError", "workAsyncStorage", "window", "require", "undefined", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "React", "Component", "getDerivedStateFromError", "isNextRouterError", "getDerivedStateFromProps", "props", "state", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "handleHardNavError", "previousPathname", "pathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p", "useUntrackedPathname"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;IAiMgBA,aAAa,EAAA;eAAbA;;IAxHHC,oBAAoB,EAAA;eAApBA;;IAoFGC,WAAW,EAAA;eAAXA;;IAuBhB,gFAAgF;IAChF,2CAA2C;IAC3C,OAA0B,EAAA;eAA1B;;;;;gEApLgC;qCACK;mCACH;mCACC;AAEnC,MAAMC,mBACJ,OAAOC,WAAW,cAEZC,QAAQ,uKACRF,gBAAgB,GAClBG;AAEN,MAAMC,SAAS;IACbC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,UAAU;QACVC,YAAY;QACZC,YAAY;QACZC,QAAQ;IACV;AACF;AA0BA,8DAA8D;AAC9D,yDAAyD;AACzD,oCAAoC;AACpC,SAASC,eAAe,KAAyB;IAAzB,IAAA,EAAEb,KAAK,EAAkB,GAAzB;IACtB,IAAIL,kBAAkB;QACpB,MAAMmB,QAAQnB,iBAAiBoB,QAAQ;QACvC,IAAID,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAOE,YAAY,KAAA,CAAIF,SAAAA,OAAAA,KAAAA,IAAAA,MAAOG,kBAAkB,GAAE;YACpDC,QAAQlB,KAAK,CAACA;YACd,MAAMA;QACR;IACF;IAEA,OAAO;AACT;AAEO,MAAMP,6BAA6B0B,OAAAA,OAAK,CAACC,SAAS;IASvD,OAAOC,yBAAyBrB,KAAY,EAAE;QAC5C,IAAIsB,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACtB,QAAQ;YAC5B,+DAA+D;YAC/D,4GAA4G;YAC5G,MAAMA;QACR;QAEA,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOuB,yBACLC,KAAgC,EAChCC,KAAgC,EACE;QAClC,MAAM,EAAEzB,KAAK,EAAE,GAAGyB;QAElB,iCAAiC;QACjC,8CAA8C;QAC9C,iDAAiD;QACjD,6CAA6C;QAC7C,IAAIC,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;QAQ5C;QAEA;;;;;KAKC,GACD,IAAIJ,MAAMO,QAAQ,KAAKN,MAAMK,gBAAgB,IAAIL,MAAMzB,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACP8B,kBAAkBN,MAAMO,QAAQ;YAClC;QACF;QACA,OAAO;YACL/B,OAAOyB,MAAMzB,KAAK;YAClB8B,kBAAkBN,MAAMO,QAAQ;QAClC;IACF;IAMA,yIAAyI;IACzIC,SAA0B;QACxB,IAAI,IAAI,CAACP,KAAK,CAACzB,KAAK,EAAE;YACpB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;kCACE,CAAA,GAAA,YAAA,GAAA,EAACa,gBAAAA;wBAAeb,OAAO,IAAI,CAACyB,KAAK,CAACzB,KAAK;;oBACtC,IAAI,CAACwB,KAAK,CAACS,WAAW;oBACtB,IAAI,CAACT,KAAK,CAACU,YAAY;kCACxB,CAAA,GAAA,YAAA,GAAA,EAACC,IAAI,CAACX,KAAK,CAACY,cAAc,EAAA;wBACxBpC,OAAO,IAAI,CAACyB,KAAK,CAACzB,KAAK;wBACvBqC,OAAO,IAAI,CAACA,KAAK;;;;QAIzB;QAEA,OAAO,IAAI,CAACb,KAAK,CAACc,QAAQ;IAC5B;IA1EAC,YAAYf,KAAgC,CAAE;QAC5C,KAAK,CAACA,QAAAA,IAAAA,CAoDRa,KAAAA,GAAQ;YACN,IAAI,CAACG,QAAQ,CAAC;gBAAExC,OAAO;YAAK;QAC9B;QArDE,IAAI,CAACyB,KAAK,GAAG;YAAEzB,OAAO;YAAM8B,kBAAkB,IAAI,CAACN,KAAK,CAACO,QAAQ;QAAC;IACpE;AAwEF;AAKO,SAASrC,YAAY,KAAyB;IAAzB,IAAA,EAAEM,KAAK,EAAkB,GAAzB;IAC1B,MAAMyC,SAA6BzC,SAAAA,OAAAA,KAAAA,IAAAA,MAAOyC,MAAM;IAChD,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACC,QAAAA;QAAKC,IAAG;;0BACP,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA,CAAAA;0BACD,CAAA,GAAA,YAAA,IAAA,EAACC,QAAAA;;kCACC,CAAA,GAAA,YAAA,GAAA,EAAChC,gBAAAA;wBAAeb,OAAOA;;kCACvB,CAAA,GAAA,YAAA,GAAA,EAAC8C,OAAAA;wBAAIC,OAAOhD,OAAOC,KAAK;kCACtB,WAAA,GAAA,CAAA,GAAA,YAAA,IAAA,EAAC8C,OAAAA;;8CACC,CAAA,GAAA,YAAA,IAAA,EAACE,MAAAA;oCAAGD,OAAOhD,OAAOS,IAAI;;wCAAE;wCACAiC,SAAS,WAAW;wCAAS;wCACvB7C,OAAOqD,QAAQ,CAACC,QAAQ;wCAAC;wCAAU;wCAC9DT,SAAS,gBAAgB;wCAAkB;;;gCAG7CA,SAAAA,WAAAA,GAAS,CAAA,GAAA,YAAA,GAAA,EAACU,KAAAA;oCAAEJ,OAAOhD,OAAOS,IAAI;8CAAI,aAAUiC;qCAAgB;;;;;;;;AAMzE;MAIA,WAAe/C;AAWR,SAASF,cAAc,KAO7B;IAP6B,IAAA,EAC5B4C,cAAc,EACdH,WAAW,EACXC,YAAY,EACZI,QAAQ,EAGT,GAP6B;IAQ5B,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,oEAAoE;IACpE,MAAMP,WAAWqB,CAAAA,GAAAA,qBAAAA,oBAAoB;IACrC,IAAIhB,gBAAgB;QAClB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC3C,sBAAAA;YACCsC,UAAUA;YACVK,gBAAgBA;YAChBH,aAAaA;YACbC,cAAcA;sBAEbI;;IAGP;IAEA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0], "debugId": null}}]}