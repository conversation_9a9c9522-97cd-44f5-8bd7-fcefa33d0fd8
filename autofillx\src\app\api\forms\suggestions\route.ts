import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { getFormSuggestions, getBusinessSegments } from '@/lib/ai-service'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { businessType, customDescription } = await request.json()

    if (!businessType) {
      return NextResponse.json({ error: 'Business type is required' }, { status: 400 })
    }

    const suggestions = await getFormSuggestions(businessType, customDescription)

    return NextResponse.json({ suggestions })
  } catch (error) {
    console.error('Error getting form suggestions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const segments = getBusinessSegments()
    return NextResponse.json({ segments })
  } catch (error) {
    console.error('Error getting business segments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
