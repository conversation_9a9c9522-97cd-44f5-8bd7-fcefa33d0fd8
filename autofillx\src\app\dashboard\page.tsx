"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import DashboardLayout from "@/components/dashboard/layout"
import { 
  FileText, 
  Users, 
  TrendingUp, 
  Plus,
  BarChart3,
  Clock,
  CheckCircle
} from "lucide-react"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return // Still loading
    if (!session) router.push("/login")
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const stats = [
    {
      name: "Total de Formulários",
      value: "12",
      icon: FileText,
      change: "+2",
      changeType: "increase",
    },
    {
      name: "Respostas Recebidas",
      value: "1,234",
      icon: Users,
      change: "+12%",
      changeType: "increase",
    },
    {
      name: "Taxa de Conversão",
      value: "68%",
      icon: TrendingUp,
      change: "+5%",
      changeType: "increase",
    },
    {
      name: "Tempo Médio",
      value: "3.2min",
      icon: Clock,
      change: "-0.5min",
      changeType: "decrease",
    },
  ]

  const recentForms = [
    {
      id: 1,
      name: "Financiamento Veicular",
      responses: 45,
      status: "active",
      lastResponse: "2 horas atrás",
    },
    {
      id: 2,
      name: "Cadastro Imobiliário",
      responses: 23,
      status: "active",
      lastResponse: "5 horas atrás",
    },
    {
      id: 3,
      name: "Consulta Médica",
      responses: 67,
      status: "active",
      lastResponse: "1 dia atrás",
    },
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Bem-vindo, {session.user?.name}!
            </h1>
            <p className="text-gray-600">
              Aqui está um resumo da sua atividade
            </p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>Novo Formulário</span>
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <div key={stat.name} className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg">
                  <stat.icon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span
                  className={`text-sm font-medium ${
                    stat.changeType === "increase"
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500 ml-2">vs. mês anterior</span>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Forms */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Formulários Recentes
              </h2>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                Ver todos
              </button>
            </div>
            <div className="space-y-4">
              {recentForms.map((form) => (
                <div key={form.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <FileText className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{form.name}</p>
                      <p className="text-sm text-gray-500">
                        {form.responses} respostas • {form.lastResponse}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-xs text-green-600 font-medium">Ativo</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Ações Rápidas
            </h2>
            <div className="space-y-3">
              <button className="w-full flex items-center space-x-3 p-3 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                <Plus className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-gray-900">Criar Formulário</p>
                  <p className="text-sm text-gray-500">Use IA para sugestões automáticas</p>
                </div>
              </button>
              
              <button className="w-full flex items-center space-x-3 p-3 text-left bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                <BarChart3 className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-gray-900">Ver Analytics</p>
                  <p className="text-sm text-gray-500">Acompanhe performance e conversões</p>
                </div>
              </button>
              
              <button className="w-full flex items-center space-x-3 p-3 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                <Users className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium text-gray-900">Exportar Dados</p>
                  <p className="text-sm text-gray-500">Baixe respostas em CSV ou PDF</p>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Getting Started */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold mb-2">
                🚀 Comece agora mesmo!
              </h2>
              <p className="text-blue-100 mb-4">
                Crie seu primeiro formulário com IA assistente em menos de 5 minutos
              </p>
              <button className="bg-white text-blue-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Criar Primeiro Formulário
              </button>
            </div>
            <div className="hidden md:block">
              <div className="bg-white/10 p-4 rounded-lg">
                <FileText className="h-12 w-12 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
