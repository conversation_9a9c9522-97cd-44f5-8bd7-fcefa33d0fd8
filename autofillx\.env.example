# Database
DATABASE_URL="postgresql://username:password@localhost:5432/autofillx"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OpenAI
OPENAI_API_KEY="your-openai-api-key-here"

# Email (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Encryption key for sensitive data
ENCRYPTION_KEY="your-32-character-encryption-key"

# File upload
UPLOAD_MAX_SIZE="10485760" # 10MB in bytes
ALLOWED_FILE_TYPES="pdf,doc,docx,jpg,jpeg,png"

# Rate limiting
RATE_LIMIT_MAX="100" # requests per window
RATE_LIMIT_WINDOW="900000" # 15 minutes in milliseconds
