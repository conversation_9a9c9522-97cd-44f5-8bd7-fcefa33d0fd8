// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  forms     Form[]
  responses FormResponse[]

  @@map("users")
}

model Form {
  id          String   @id @default(cuid())
  title       String
  description String?
  slug        String   @unique
  isActive    Boolean  @default(true)
  settings    Json? // Form settings like styling, notifications, etc.
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId    String
  user      User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  fields    FormField[]
  responses FormResponse[]

  @@map("forms")
}

model FormField {
  id          String    @id @default(cuid())
  type        FieldType
  label       String
  placeholder String?
  required    <PERSON><PERSON><PERSON>   @default(false)
  options     Json? // For select, radio, checkbox fields
  validation  Json? // Validation rules
  order       Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  formId String
  form   Form   @relation(fields: [formId], references: [id], onDelete: Cascade)

  @@map("form_fields")
}

model FormResponse {
  id        String   @id @default(cuid())
  data      Json // The actual form response data
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  formId String
  form   Form    @relation(fields: [formId], references: [id], onDelete: Cascade)
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("form_responses")
}

model Integration {
  id        String          @id @default(cuid())
  name      String
  type      IntegrationType
  config    Json // Integration configuration
  isActive  Boolean         @default(true)
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt

  @@map("integrations")
}

enum UserRole {
  USER
  ADMIN
}

enum FieldType {
  TEXT
  EMAIL
  PHONE
  NUMBER
  TEXTAREA
  SELECT
  RADIO
  CHECKBOX
  DATE
  FILE
  CPF
  CNPJ
}

enum IntegrationType {
  WEBHOOK
  EMAIL
  PDF_GENERATOR
  API
  ZAPIER
}
